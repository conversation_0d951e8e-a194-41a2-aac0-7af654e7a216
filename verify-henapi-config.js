#!/usr/bin/env node

/**
 * Verify HenAPI configuration
 * This script checks if the environment variables are correctly set
 */

require('dotenv').config({ path: '.env.local' });

console.log("🔍 Verifying HenAPI Configuration");
console.log("=" .repeat(50));

console.log("\n📋 Environment Variables:");
console.log(`AI_GENERATION_MODE: ${process.env.AI_GENERATION_MODE || 'NOT_SET'}`);
console.log(`HENAPI_API_KEY: ${process.env.HENAPI_API_KEY ? 'SET (***' + process.env.HENAPI_API_KEY.slice(-4) + ')' : 'NOT_SET'}`);
console.log(`HENAPI_BASE_URL: ${process.env.HENAPI_BASE_URL || 'NOT_SET'}`);
console.log(`OPENAI_API_KEY: ${process.env.OPENAI_API_KEY ? 'SET' : 'NOT_SET'}`);

console.log("\n✅ Configuration Status:");

// Check AI_GENERATION_MODE
if (process.env.AI_GENERATION_MODE === 'henapi') {
  console.log("✅ AI_GENERATION_MODE is set to 'henapi'");
} else if (process.env.AI_GENERATION_MODE === 'openai') {
  console.log("⚠️  AI_GENERATION_MODE is set to 'openai' (will use OpenAI instead of HenAPI)");
} else {
  console.log("❌ AI_GENERATION_MODE is not set or invalid");
}

// Check HENAPI_API_KEY
if (process.env.HENAPI_API_KEY) {
  console.log("✅ HENAPI_API_KEY is configured");
  
  // Validate API key format
  if (process.env.HENAPI_API_KEY.startsWith('sk-')) {
    console.log("✅ HENAPI_API_KEY has correct format (starts with 'sk-')");
  } else {
    console.log("⚠️  HENAPI_API_KEY format might be incorrect (doesn't start with 'sk-')");
  }
} else {
  console.log("❌ HENAPI_API_KEY is not configured");
}

// Check HENAPI_BASE_URL
if (process.env.HENAPI_BASE_URL) {
  console.log("✅ HENAPI_BASE_URL is configured");
  
  // Validate URL format
  if (process.env.HENAPI_BASE_URL === 'https://www.henapi.top/v1') {
    console.log("✅ HENAPI_BASE_URL is correct");
  } else if (process.env.HENAPI_BASE_URL.includes('/images/generations')) {
    console.log("⚠️  HENAPI_BASE_URL includes endpoint path - should be base URL only");
    console.log(`   Current: ${process.env.HENAPI_BASE_URL}`);
    console.log(`   Should be: https://www.henapi.top/v1`);
  } else {
    console.log(`⚠️  HENAPI_BASE_URL might be incorrect: ${process.env.HENAPI_BASE_URL}`);
  }
} else {
  console.log("❌ HENAPI_BASE_URL is not configured");
}

console.log("\n🚀 Next Steps:");

if (process.env.AI_GENERATION_MODE === 'henapi' && process.env.HENAPI_API_KEY && process.env.HENAPI_BASE_URL === 'https://www.henapi.top/v1') {
  console.log("✅ Configuration looks good!");
  console.log("1. Restart your development server: pnpm dev");
  console.log("2. Test the API: curl http://localhost:3000/api/generate");
  console.log("3. Try generating images through the web interface");
} else {
  console.log("❌ Configuration needs fixes:");
  
  if (process.env.AI_GENERATION_MODE !== 'henapi') {
    console.log("- Set AI_GENERATION_MODE=henapi in .env.local");
  }
  
  if (!process.env.HENAPI_API_KEY) {
    console.log("- Add HENAPI_API_KEY=your_api_key in .env.local");
  }
  
  if (process.env.HENAPI_BASE_URL !== 'https://www.henapi.top/v1') {
    console.log("- Set HENAPI_BASE_URL=https://www.henapi.top/v1 in .env.local");
  }
  
  console.log("\nAfter making changes:");
  console.log("1. Restart your development server");
  console.log("2. Run this script again to verify");
}

console.log("\n" + "=" .repeat(50));
