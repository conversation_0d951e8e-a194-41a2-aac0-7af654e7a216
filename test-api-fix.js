// Test script to verify the API fixes
// This script tests the /api/generate endpoint with proper logging

const testData = {
  photoUrl: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=",
  styles: ["chinese-traditional", "western-elegant"], 
  userId: "test-user"
};

console.log("🧪 Testing API endpoint with enhanced logging");
console.log("📋 Test data:", {
  photoUrlLength: testData.photoUrl.length,
  stylesCount: testData.styles.length,
  styles: testData.styles,
  userId: testData.userId
});

const startTime = Date.now();

fetch('http://localhost:3000/api/generate', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(testData),
})
.then(response => {
  const endTime = Date.now();
  console.log("📥 Response received:", {
    status: response.status,
    statusText: response.statusText,
    ok: response.ok,
    duration: `${endTime - startTime}ms`
  });
  return response.json();
})
.then(data => {
  console.log("📄 API Response:", {
    success: data.success,
    requestId: data.requestId,
    photosCount: data.photos?.length || 0,
    jobId: data.jobId,
    status: data.status,
    summary: data.summary
  });
  
  if (data.photos && data.photos.length > 0) {
    console.log("🖼️ Generated photos:");
    data.photos.forEach((photo, index) => {
      console.log(`  ${index + 1}. Style: ${photo.style}, Has Error: ${!!photo.error}`);
      if (photo.error) {
        console.log(`     Error: ${photo.error}`);
      }
    });
  }
  
  if (data.error) {
    console.error("❌ API Error:", data.error);
    console.error("❌ Error details:", data.details);
  }
})
.catch(error => {
  console.error("❌ Network/Fetch Error:", error);
});

console.log("⏳ Request sent, waiting for response...");
