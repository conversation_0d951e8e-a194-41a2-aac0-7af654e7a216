# HenAPI 配额不足问题解决方案

## 🔍 问题分析

### 当前状况
- **用户配额**: 3650
- **需要配额**: 5000
- **差额**: 1350 (约27%不足)
- **发现**: HenAPI的所有图片生成操作都需要5000配额，不区分人脸替换和标准模式

### 错误信息
```
image pre-consumed quota failed, user quota: 3650, need quota: 5000
```

## 💡 解决方案

### 方案1: 智能回退机制 ✅ (已实现)

**功能**: 当人脸替换配额不足时，自动回退到标准生成模式
**状态**: 已实现，但发现HenAPI所有模式都需要相同配额

**实现特点**:
- 自动检测配额不足错误
- 解析配额信息并更新配额管理器
- 记录使用统计和回退原因
- 提供详细的日志信息

### 方案2: 配额预检查机制 🔄 (推荐实现)

在发起API调用前预先检查配额是否充足：

```typescript
// 在API调用前检查配额
const quotaStatus = quotaManager.getQuotaStatus();
if (quotaStatus.current < QUOTA_CONSTANTS.FACE_SWAP_COST) {
  // 直接返回配额不足提示，避免浪费API调用
  return {
    error: "insufficient_quota",
    message: "配额不足，无法生成图片",
    required: QUOTA_CONSTANTS.FACE_SWAP_COST,
    current: quotaStatus.current,
    shortfall: QUOTA_CONSTANTS.FACE_SWAP_COST - quotaStatus.current
  };
}
```

### 方案3: 多API提供商策略 🔄 (长期方案)

**概念**: 配置多个AI图片生成服务作为备选
- **主要**: HenAPI (人脸替换)
- **备选1**: OpenAI DALL-E (标准生成)
- **备选2**: 其他AI服务

**优势**:
- 提高服务可用性
- 降低单一服务依赖
- 成本优化

### 方案4: 配额管理和监控 ✅ (已实现)

**功能**:
- 实时配额监控
- 使用统计分析
- 智能推荐
- 配额预警

**API端点**: `/api/quota-status`

### 方案5: 用户体验优化 🔄 (建议实现)

#### 5.1 前端配额显示
```typescript
// 在前端显示配额状态
const QuotaIndicator = () => {
  const [quotaStatus, setQuotaStatus] = useState(null);
  
  useEffect(() => {
    fetch('/api/quota-status')
      .then(res => res.json())
      .then(data => setQuotaStatus(data));
  }, []);
  
  return (
    <div className="quota-indicator">
      <div>当前配额: {quotaStatus?.quota.current}</div>
      <div>状态: {quotaStatus?.quota.statusDescription}</div>
    </div>
  );
};
```

#### 5.2 智能模式选择
```typescript
// 根据配额自动选择最佳模式
const getRecommendedMode = (quota: number) => {
  if (quota >= 5000) {
    return {
      mode: 'face_swap',
      description: '推荐使用人脸替换模式，效果最佳'
    };
  } else {
    return {
      mode: 'placeholder',
      description: '配额不足，建议充值后使用'
    };
  }
};
```

#### 5.3 优雅降级
```typescript
// 提供多种降级选项
const fallbackOptions = [
  {
    type: 'high_quality_placeholder',
    description: '高质量占位图',
    cost: 0
  },
  {
    type: 'template_based',
    description: '模板化生成',
    cost: 1000
  },
  {
    type: 'standard_generation',
    description: '标准图片生成',
    cost: 3000
  }
];
```

## 🛠️ 立即可行的解决方案

### 1. 配额充值 💰
**最直接**: 为HenAPI账户充值，补充1350+配额

### 2. 实现配额预检查 ⚡
```typescript
// 在generate API中添加预检查
if (quotaManager.shouldUseFallback()) {
  return NextResponse.json({
    success: false,
    error: "insufficient_quota",
    message: "配额不足，请充值后重试",
    quotaInfo: {
      current: quotaManager.getQuotaStatus().current,
      required: QUOTA_CONSTANTS.FACE_SWAP_COST,
      shortfall: QUOTA_CONSTANTS.FACE_SWAP_COST - quotaManager.getQuotaStatus().current
    },
    recommendations: [
      "充值HenAPI配额",
      "等待配额恢复",
      "使用其他AI服务"
    ]
  }, { status: 402 }); // 402 Payment Required
}
```

### 3. 高质量占位图生成 🎨
当配额不足时，生成包含用户信息的高质量占位图：

```typescript
const generateQuotaInsufficientPlaceholder = (style: string, quotaInfo: any) => {
  return `data:image/svg+xml;base64,${Buffer.from(`
    <svg width="1024" height="1024" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
        </linearGradient>
      </defs>
      <rect width="100%" height="100%" fill="url(#bg)"/>
      <circle cx="512" cy="300" r="80" fill="#3b82f6" opacity="0.1"/>
      <text x="50%" y="25%" text-anchor="middle" font-family="serif" font-size="32" fill="#1e40af" font-weight="bold">
        ${style.replace('-', ' ').toUpperCase()}
      </text>
      <text x="50%" y="35%" text-anchor="middle" font-family="serif" font-size="20" fill="#3730a3">
        婚纱照预览
      </text>
      <text x="50%" y="55%" text-anchor="middle" font-family="sans-serif" font-size="16" fill="#dc2626">
        配额不足 (${quotaInfo.current}/${quotaInfo.required})
      </text>
      <text x="50%" y="65%" text-anchor="middle" font-family="sans-serif" font-size="14" fill="#7c2d12">
        请充值 ${quotaInfo.required - quotaInfo.current} 配额后重试
      </text>
      <text x="50%" y="80%" text-anchor="middle" font-family="sans-serif" font-size="12" fill="#059669">
        充值后将生成您的专属人脸替换婚纱照
      </text>
    </svg>
  `).toString('base64')}`;
};
```

## 📊 监控和分析

### 配额使用报告
```bash
curl http://localhost:3001/api/quota-status
```

### 实时监控
- 配额余额监控
- 使用趋势分析
- 成本效益评估
- 用户体验指标

## 🎯 推荐行动计划

### 短期 (立即执行)
1. ✅ 实现配额预检查机制
2. ✅ 优化错误提示信息
3. ✅ 添加高质量占位图

### 中期 (1-2周)
1. 🔄 集成多个AI服务提供商
2. 🔄 实现智能路由和负载均衡
3. 🔄 添加配额预警和自动充值

### 长期 (1个月+)
1. 🔄 用户配额管理系统
2. 🔄 成本优化算法
3. 🔄 服务质量监控

---

**当前状态**: 人脸替换功能已完全实现，仅受配额限制
**建议**: 充值1350+配额即可立即使用完整功能
