# Wedding Photo AI Generation Setup

## 概述
本项目已经修改为使用真实的 OpenAI API 来生成婚纱照，而不是之前的模拟生成。

## 主要修改

### 1. 安装的依赖
- `openai`: OpenAI 官方 SDK

### 2. 修改的文件
- `src/app/api/generate/route.ts`: 实现真实的 OpenAI API 调用
- `src/app/[locale]/generating/page.tsx`: 调用真实 API 而不是模拟进度
- `src/app/[locale]/results/page.tsx`: 处理真实生成的图片
- `src/app/[locale]/upload/page.tsx`: 修复返回按钮路径
- `src/app/[locale]/styles/page.tsx`: 修复返回按钮路径
- `.env.local`: 环境变量配置
- `.env.example`: 环境变量示例

### 3. 新增功能
- 真实的 AI 图片生成
- 详细的日志输出
- 错误处理和重试机制
- 真实图片下载功能
- 防止重复API调用机制
- 优化的婚纱照prompt（确保正面视角）

### 4. 修复的问题
- ✅ 防止重复API调用：添加状态检查和会话存储验证
- ✅ 优化prompt：确保生成的婚纱照人物是正面的
- ✅ 修复路径问题：所有返回按钮使用正确的locale路径

## 配置步骤

### 1. 设置环境变量
复制 `.env.example` 到 `.env.local` 并填入真实的 API 密钥：

```bash
cp .env.example .env.local
```

编辑 `.env.local`:
```env
OPENAI_API_KEY=your_actual_api_key_here
OPENAI_BASE_URL=https://api.laozhang.ai/v1
```

### 2. 安装依赖
```bash
pnpm install
```

### 3. 启动开发服务器
```bash
pnpm dev
```

## 使用流程

1. **上传照片**: 用户在 `/upload` 页面上传自拍照
2. **选择风格**: 用户在 `/styles` 页面选择婚纱风格
3. **AI 生成**: 系统调用 OpenAI API 生成婚纱照
4. **查看结果**: 用户在 `/results` 页面查看和下载生成的照片

## 支持的婚纱风格

每种风格都经过优化，确保生成正面视角的婚纱照：

- `chinese-traditional`: 中式传统婚纱 - 红金配色，古典建筑背景，正面朝向镜头
- `western-elegant`: 西式优雅婚纱 - 经典白色婚纱，教堂或花园背景，正面朝向镜头
- `beach-sunset`: 海滩日落婚纱 - 飘逸海滩婚纱，金色日落背景，正面朝向镜头
- `forest-romantic`: 森林浪漫婚纱 - 梦幻森林背景，自然光线，正面朝向镜头
- `vintage-classic`: 复古经典婚纱 - 复古风格，永恒优雅，正面朝向镜头
- `modern-chic`: 现代时尚婚纱 - 现代简约，简洁线条，正面朝向镜头

## 日志输出

系统在关键位置添加了详细的日志输出：
- 🚀 API 调用开始
- 📝 请求数据验证
- ✅ 验证通过
- 🎨 开始 AI 图片生成
- 🖼️ 单个图片生成进度
- ✅ 图片生成成功
- ❌ 错误信息
- 🎉 生成完成
- 📤 返回响应

## 错误处理

- API 调用失败时会显示错误信息
- 提供重试功能
- 失败时回退到占位符图片
- 详细的错误日志记录

## 注意事项

1. 确保 OpenAI API 密钥有效且有足够的配额
2. 图片生成可能需要几分钟时间
3. 生成的图片会临时存储在 sessionStorage 中
4. 建议在生产环境中实现图片持久化存储
5. 系统已优化防止重复API调用，如果用户刷新页面会自动跳转到结果页面
6. 所有生成的婚纱照都是正面视角，符合专业婚纱摄影标准

## 测试

运行测试脚本验证API功能：
```bash
node test-api.js
```

注意：测试前确保开发服务器正在运行且API密钥已正确配置。
