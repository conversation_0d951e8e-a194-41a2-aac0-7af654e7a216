'use client';

import { useState, useEffect } from 'react';

interface QuotaInfo {
  current: number;
  required: number;
  standardRequired: number;
  shortfall: number;
  percentage: string;
  lastUpdated: string;
  canUseStandard: boolean;
}

interface UsageStats {
  totalOperations: number;
  successfulOperations: number;
  fallbackOperations: number;
  successRate: number;
  fallbackRate: number;
}

interface QuotaStatus {
  current: number;
  lastUpdated: string;
  recentUsage: Array<{
    timestamp: string;
    operation: string;
    quotaUsed: number;
    quotaRemaining: number;
    success: boolean;
    fallbackUsed?: boolean;
  }>;
  fallbackRecommended: boolean;
}

export default function DebugQuotaPage() {
  const [apiConfig, setApiConfig] = useState<any>(null);
  const [quotaInfo, setQuotaInfo] = useState<QuotaInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchApiConfig = async () => {
    try {
      const response = await fetch('/api/generate');
      const data = await response.json();
      setApiConfig(data);
    } catch (err) {
      setError('获取API配置失败');
    }
  };

  const testQuotaStatus = async () => {
    try {
      setLoading(true);
      const formData = new FormData();
      
      // 创建一个小的测试图片
      const canvas = document.createElement('canvas');
      canvas.width = 100;
      canvas.height = 100;
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.fillStyle = '#f0f0f0';
        ctx.fillRect(0, 0, 100, 100);
        ctx.fillStyle = '#333';
        ctx.font = '12px Arial';
        ctx.fillText('Test', 35, 55);
      }
      
      canvas.toBlob((blob) => {
        if (blob) {
          formData.append('photoUrl', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
          formData.append('styles', JSON.stringify(['modern-chic']));
          formData.append('userId', 'debug-test');
          
          fetch('/api/generate', {
            method: 'POST',
            body: formData
          })
          .then(response => response.json())
          .then(data => {
            if (data.error === 'insufficient_quota' && data.quotaInfo) {
              setQuotaInfo(data.quotaInfo);
            }
            setLoading(false);
          })
          .catch(err => {
            setError('配额测试失败');
            setLoading(false);
          });
        }
      }, 'image/png');
    } catch (err) {
      setError('配额测试失败');
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchApiConfig();
    testQuotaStatus();
  }, []);

  const refreshData = () => {
    setError(null);
    fetchApiConfig();
    testQuotaStatus();
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">配额调试面板</h1>
          <button
            onClick={refreshData}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            刷新数据
          </button>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <div className="text-red-800">
              <strong>错误：</strong> {error}
            </div>
          </div>
        )}

        {/* API配置信息 */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">API配置状态</h2>
          {apiConfig ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-medium text-gray-700 mb-2">基本配置</h3>
                <div className="space-y-1 text-sm">
                  <p><span className="font-medium">当前模式:</span> {apiConfig.currentMode}</p>
                  <p><span className="font-medium">支持模式:</span> {apiConfig.supportedModes?.join(', ')}</p>
                </div>
              </div>
              <div>
                <h3 className="font-medium text-gray-700 mb-2">API密钥状态</h3>
                <div className="space-y-1 text-sm">
                  <p><span className="font-medium">OpenAI:</span> 
                    <span className={`ml-2 px-2 py-1 rounded text-xs ${apiConfig.configuration?.hasOpenAIKey ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                      {apiConfig.configuration?.hasOpenAIKey ? '已配置' : '未配置'}
                    </span>
                  </p>
                  <p><span className="font-medium">HenAPI:</span> 
                    <span className={`ml-2 px-2 py-1 rounded text-xs ${apiConfig.configuration?.hasHenAPIKey ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                      {apiConfig.configuration?.hasHenAPIKey ? '已配置' : '未配置'}
                    </span>
                  </p>
                  <p><span className="font-medium">APICore:</span> 
                    <span className={`ml-2 px-2 py-1 rounded text-xs ${apiConfig.configuration?.hasAPICoreKey ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                      {apiConfig.configuration?.hasAPICoreKey ? '已配置' : '未配置'}
                    </span>
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-gray-500">加载中...</div>
          )}
        </div>

        {/* 配额状态 */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">HenAPI配额状态</h2>
          {loading ? (
            <div className="text-gray-500">检测配额状态中...</div>
          ) : quotaInfo ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h3 className="font-medium text-blue-800 mb-2">当前配额</h3>
                  <p className="text-2xl font-bold text-blue-900">{quotaInfo.current.toLocaleString()}</p>
                </div>
                <div className="bg-orange-50 p-4 rounded-lg">
                  <h3 className="font-medium text-orange-800 mb-2">人脸替换需求</h3>
                  <p className="text-2xl font-bold text-orange-900">{quotaInfo.required.toLocaleString()}</p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <h3 className="font-medium text-green-800 mb-2">标准生成需求</h3>
                  <p className="text-2xl font-bold text-green-900">{quotaInfo.standardRequired.toLocaleString()}</p>
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-medium text-gray-800 mb-2">配额分析</h3>
                <div className="space-y-2 text-sm">
                  <p><span className="font-medium">配额不足:</span> {quotaInfo.shortfall.toLocaleString()}</p>
                  <p><span className="font-medium">配额利用率:</span> {quotaInfo.percentage}%</p>
                  <p><span className="font-medium">可使用标准生成:</span> 
                    <span className={`ml-2 px-2 py-1 rounded text-xs ${quotaInfo.canUseStandard ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                      {quotaInfo.canUseStandard ? '是' : '否'}
                    </span>
                  </p>
                  <p><span className="font-medium">最后更新:</span> {new Date(quotaInfo.lastUpdated).toLocaleString()}</p>
                </div>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
                <h3 className="font-medium text-yellow-800 mb-2">解决方案</h3>
                <ul className="text-sm text-yellow-700 space-y-1">
                  <li>• 为HenAPI账户充值配额</li>
                  <li>• 需要补充 {quotaInfo.shortfall.toLocaleString()} 配额用于人脸替换</li>
                  {quotaInfo.canUseStandard && <li>• 当前可以使用标准生成模式</li>}
                  <li>• 联系管理员获取更多配额</li>
                  <li>• 切换到OpenAI或APICore模式</li>
                </ul>
              </div>
            </div>
          ) : (
            <div className="text-gray-500">
              {apiConfig?.currentMode === 'henapi' ? '配额检测失败' : '当前不是HenAPI模式'}
            </div>
          )}
        </div>

        {/* 调试建议 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">调试建议</h2>
          <div className="space-y-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-medium text-blue-800 mb-2">启用调试模式</h3>
              <p className="text-sm text-blue-700 mb-2">在 .env.local 中添加以下配置以获取更详细的日志：</p>
              <code className="block bg-blue-100 p-2 rounded text-sm">
                DEBUG_MODE=true
              </code>
            </div>

            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="font-medium text-green-800 mb-2">切换到其他模式</h3>
              <p className="text-sm text-green-700 mb-2">如果HenAPI配额不足，可以切换到其他模式：</p>
              <div className="space-y-1 text-sm">
                <code className="block bg-green-100 p-2 rounded">AI_GENERATION_MODE=openai</code>
                <code className="block bg-green-100 p-2 rounded">AI_GENERATION_MODE=apicore</code>
              </div>
            </div>

            <div className="bg-purple-50 p-4 rounded-lg">
              <h3 className="font-medium text-purple-800 mb-2">开发模式</h3>
              <p className="text-sm text-purple-700 mb-2">在开发环境中使用占位图片：</p>
              <code className="block bg-purple-100 p-2 rounded text-sm">
                USE_PLACEHOLDER_IMAGES=true
              </code>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
