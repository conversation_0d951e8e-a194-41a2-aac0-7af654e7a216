import { NextRequest, NextResponse } from "next/server";
import { quotaManager, QUOTA_CONSTANTS } from "@/lib/quota-manager";

// 调试配额管理API
// 仅在开发环境中可用

export async function GET() {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  if (!isDevelopment) {
    return NextResponse.json({
      error: "This endpoint is only available in development mode"
    }, { status: 403 });
  }

  try {
    const quotaStatus = quotaManager.getQuotaStatus();
    const usageStats = quotaManager.getUsageStats();
    const quotaReport = quotaManager.generateReport();

    return NextResponse.json({
      success: true,
      quotaStatus: {
        current: quotaStatus.current,
        lastUpdated: quotaStatus.lastUpdated.toISOString(),
        fallbackRecommended: quotaStatus.fallbackRecommended,
        recentUsage: quotaStatus.recentUsage
      },
      usageStats,
      constants: QUOTA_CONSTANTS,
      report: quotaReport,
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        AI_GENERATION_MODE: process.env.AI_GENERATION_MODE,
        DEBUG_MODE: process.env.DEBUG_MODE,
        USE_PLACEHOLDER_IMAGES: process.env.USE_PLACEHOLDER_IMAGES
      }
    });
  } catch (error) {
    return NextResponse.json({
      error: "Failed to get quota status",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  if (!isDevelopment) {
    return NextResponse.json({
      error: "This endpoint is only available in development mode"
    }, { status: 403 });
  }

  try {
    const body = await request.json();
    const { action, quota } = body;

    switch (action) {
      case 'reset':
        // 重置配额到指定值
        const resetQuota = quota || 10000; // 默认重置到10000
        quotaManager.updateQuota(resetQuota);
        
        return NextResponse.json({
          success: true,
          message: `Quota reset to ${resetQuota}`,
          newQuota: resetQuota
        });

      case 'simulate_usage':
        // 模拟配额使用
        const usageAmount = quota || QUOTA_CONSTANTS.FACE_SWAP_COST;
        const currentQuota = quotaManager.getQuotaStatus().current;
        const newQuota = Math.max(0, currentQuota - usageAmount);
        
        quotaManager.recordUsage(
          'simulated_usage',
          usageAmount,
          newQuota,
          true,
          false
        );

        return NextResponse.json({
          success: true,
          message: `Simulated usage of ${usageAmount} quota`,
          previousQuota: currentQuota,
          newQuota: newQuota
        });

      case 'add_quota':
        // 增加配额
        const addAmount = quota || 5000;
        const currentQuotaAdd = quotaManager.getQuotaStatus().current;
        const newQuotaAdd = currentQuotaAdd + addAmount;
        
        quotaManager.updateQuota(newQuotaAdd);

        return NextResponse.json({
          success: true,
          message: `Added ${addAmount} quota`,
          previousQuota: currentQuotaAdd,
          newQuota: newQuotaAdd
        });

      case 'clear_history':
        // 清除使用历史（通过重新创建实例）
        quotaManager.updateQuota(quotaManager.getQuotaStatus().current);
        
        return NextResponse.json({
          success: true,
          message: "Usage history cleared"
        });

      default:
        return NextResponse.json({
          error: "Invalid action",
          supportedActions: ['reset', 'simulate_usage', 'add_quota', 'clear_history']
        }, { status: 400 });
    }
  } catch (error) {
    return NextResponse.json({
      error: "Failed to process quota action",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  if (!isDevelopment) {
    return NextResponse.json({
      error: "This endpoint is only available in development mode"
    }, { status: 403 });
  }

  try {
    const body = await request.json();
    const { quota } = body;

    if (typeof quota !== 'number' || quota < 0) {
      return NextResponse.json({
        error: "Invalid quota value. Must be a non-negative number."
      }, { status: 400 });
    }

    const previousQuota = quotaManager.getQuotaStatus().current;
    quotaManager.updateQuota(quota);

    return NextResponse.json({
      success: true,
      message: `Quota updated from ${previousQuota} to ${quota}`,
      previousQuota,
      newQuota: quota
    });
  } catch (error) {
    return NextResponse.json({
      error: "Failed to update quota",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}
