import { NextRequest, NextResponse } from "next/server";
import { createAPICoreService } from "@/lib/apicore";

// API endpoint for editing images using APICore
// Supports image editing with prompts and optional masks

export async function POST(request: NextRequest) {
  const requestId = `edit-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

  try {
    console.log(`🚀 [${requestId}] Starting image edit API call`);
    console.log(`🔧 [${requestId}] Environment:`, {
      NODE_ENV: process.env.NODE_ENV,
      hasAPICoreKey: !!process.env.APICORE_API_KEY,
      apicoreBaseURL: process.env.APICORE_BASE_URL || "https://api.apicore.ai/v1"
    });

    // Check APICore configuration
    const apicoreService = createAPICoreService();
    if (!apicoreService) {
      console.error(`❌ [${requestId}] APICore service not configured`);
      return NextResponse.json({
        error: "API configuration error: Missing APICore API key",
        requestId
      }, { status: 500 });
    }

    // Parse the request body
    const formData = await request.formData();
    
    // 提取参数
    const image = formData.get('image') as File | string;
    const mask = formData.get('mask') as File | string | null;
    const prompt = formData.get('prompt') as string;
    const n = formData.get('n') ? parseInt(formData.get('n') as string) : 1;
    const size = formData.get('size') as string || "1:1";
    const response_format = formData.get('response_format') as "url" | "b64_json" || "url";
    const user = formData.get('user') as string || undefined;
    const model = formData.get('model') as string || "gpt-4o-image";

    console.log(`📝 [${requestId}] Request data:`, {
      hasImage: !!image,
      imageType: image instanceof File ? 'File' : typeof image,
      imageSize: image instanceof File ? image.size : (typeof image === 'string' ? image.length : 0),
      hasMask: !!mask,
      maskType: mask instanceof File ? 'File' : typeof mask,
      prompt: prompt ? prompt.substring(0, 100) + (prompt.length > 100 ? '...' : '') : "missing",
      n,
      size,
      response_format,
      model,
      user: user || 'anonymous'
    });

    // 验证必需参数
    if (!image) {
      console.error(`❌ [${requestId}] Error: Image is required`);
      return NextResponse.json({
        error: "Image is required",
        requestId
      }, { status: 400 });
    }

    if (!prompt) {
      console.error(`❌ [${requestId}] Error: Prompt is required`);
      return NextResponse.json({
        error: "Prompt is required",
        requestId
      }, { status: 400 });
    }

    // 验证图像格式
    if (image instanceof File) {
      const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(image.type)) {
        console.error(`❌ [${requestId}] Error: Unsupported image type: ${image.type}`);
        return NextResponse.json({
          error: `Unsupported image type: ${image.type}. Supported types: ${allowedTypes.join(', ')}`,
          requestId
        }, { status: 400 });
      }

      // 检查文件大小 (4MB限制)
      const maxSize = 4 * 1024 * 1024; // 4MB
      if (image.size > maxSize) {
        console.error(`❌ [${requestId}] Error: Image too large: ${image.size} bytes`);
        return NextResponse.json({
          error: `Image too large. Maximum size is 4MB, received ${Math.round(image.size / 1024 / 1024 * 100) / 100}MB`,
          requestId
        }, { status: 400 });
      }
    }

    // 验证遮罩格式（如果提供）
    if (mask && mask instanceof File) {
      if (mask.type !== 'image/png') {
        console.error(`❌ [${requestId}] Error: Mask must be PNG format, got: ${mask.type}`);
        return NextResponse.json({
          error: "Mask must be a PNG file",
          requestId
        }, { status: 400 });
      }

      // 检查遮罩文件大小
      const maxSize = 4 * 1024 * 1024; // 4MB
      if (mask.size > maxSize) {
        console.error(`❌ [${requestId}] Error: Mask too large: ${mask.size} bytes`);
        return NextResponse.json({
          error: `Mask too large. Maximum size is 4MB, received ${Math.round(mask.size / 1024 / 1024 * 100) / 100}MB`,
          requestId
        }, { status: 400 });
      }
    }

    console.log(`✅ [${requestId}] Validation passed, proceeding with image editing`);

    try {
      // 调用APICore服务
      console.log(`🎨 [${requestId}] Calling APICore image edit service...`);
      
      const editParams = {
        image,
        mask: mask || undefined,
        prompt,
        n,
        size,
        response_format,
        user,
        model
      };

      const result = await apicoreService.editImage(editParams);

      console.log(`✅ [${requestId}] Image edit completed successfully`);
      console.log(`📊 [${requestId}] Result summary:`, {
        imagesGenerated: result.data?.length || 0,
        hasUsage: !!result.usage,
        totalTokens: result.usage?.total_tokens || 0,
        created: result.created
      });

      // 构建响应数据
      const responseData = {
        success: true,
        requestId,
        data: result.data,
        created: result.created,
        usage: result.usage,
        summary: {
          imagesGenerated: result.data?.length || 0,
          prompt: prompt,
          model: model,
          size: size,
          response_format: response_format
        }
      };

      console.log(`📤 [${requestId}] Returning successful response`);
      return NextResponse.json(responseData);

    } catch (error) {
      console.error(`❌ [${requestId}] Error during image editing:`, error);

      // 提供详细的错误信息
      let userFriendlyMessage = "Image editing failed";
      
      if (error instanceof Error) {
        console.error(`❌ [${requestId}] Error details:`, {
          message: error.message,
          name: error.name,
          stack: error.stack?.substring(0, 500)
        });

        // 处理特定错误类型
        if (error.message.includes('insufficient_user_quota')) {
          userFriendlyMessage = "API quota exceeded. Please check your API credits.";
        } else if (error.message.includes('404')) {
          userFriendlyMessage = "API endpoint not found. Please check API configuration.";
        } else if (error.message.includes('401') || error.message.includes('403')) {
          userFriendlyMessage = "API authentication failed. Please check your API key.";
        } else if (error.message.includes('rate_limit')) {
          userFriendlyMessage = "API rate limit exceeded. Please try again later.";
        } else if (error.message.includes('content_policy_violation')) {
          userFriendlyMessage = "Content policy violation. Please try a different prompt.";
        } else if (error.message.includes('invalid_image')) {
          userFriendlyMessage = "Invalid image format. Please upload a valid PNG, JPEG, or WebP image.";
        }
      }

      return NextResponse.json({
        success: false,
        error: userFriendlyMessage,
        details: error instanceof Error ? error.message : "Unknown error",
        requestId
      }, { status: 500 });
    }

  } catch (error) {
    console.error(`❌ [${requestId}] Error in image edit API:`, error);
    console.error(`❌ [${requestId}] Error details:`, {
      message: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack?.substring(0, 500) : undefined
    });

    return NextResponse.json({
      error: "Failed to process image edit request",
      details: error instanceof Error ? error.message : "Unknown error",
      requestId
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: "Image editing API using APICore",
    endpoint: "/api/edit-image",
    method: "POST",
    contentType: "multipart/form-data",
    configuration: {
      hasAPICoreKey: !!process.env.APICORE_API_KEY,
      apicoreBaseURL: process.env.APICORE_BASE_URL || "https://api.apicore.ai/v1"
    },
    parameters: {
      required: ["image", "prompt"],
      optional: ["mask", "n", "size", "response_format", "user", "model"],
      supportedFormats: ["PNG", "JPEG", "WebP", "GIF"],
      maxFileSize: "4MB",
      supportedSizes: ["1:1", "2:3", "3:2"]
    }
  });
}
