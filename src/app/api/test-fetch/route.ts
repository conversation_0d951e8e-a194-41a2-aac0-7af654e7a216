import { NextRequest, NextResponse } from "next/server";

// Test endpoint to check fetch availability and HenAPI connectivity
export async function GET(request: NextRequest) {
  const testResults = {
    timestamp: new Date().toISOString(),
    environment: {
      nodeVersion: process.version,
      hasFetch: typeof fetch !== 'undefined',
      hasGlobalFetch: typeof globalThis.fetch !== 'undefined',
      nextjsVersion: process.env.npm_package_dependencies_next || 'unknown'
    },
    henApiTest: null as any
  };

  console.log('🧪 [Test] Environment check:', testResults.environment);

  // Test basic fetch functionality
  if (typeof fetch === 'undefined') {
    return NextResponse.json({
      ...testResults,
      error: 'fetch is not available in this environment',
      suggestion: 'This indicates a problem with the Next.js setup or Node.js version'
    });
  }

  // Test HenAPI connectivity
  try {
    console.log('🧪 [Test] Testing HenAPI connectivity...');
    
    const henApiUrl = process.env.HENAPI_BASE_URL || "https://www.henapi.top/v1";
    const testEndpoint = `${henApiUrl}/images/generations`;
    
    // Simple GET request to test connectivity (will return 404 but that's OK)
    const response = await fetch(testEndpoint, {
      method: 'GET',
      headers: {
        'User-Agent': 'HenAPI-Test/1.0'
      },
      signal: AbortSignal.timeout(10000) // 10 second timeout
    });

    testResults.henApiTest = {
      url: testEndpoint,
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries()),
      success: response.status === 404 || response.status === 405 || response.status === 401, // These are expected for GET requests
      responsePreview: await response.text().then(text => text.substring(0, 200))
    };

    console.log('🧪 [Test] HenAPI test result:', testResults.henApiTest);

  } catch (error) {
    console.log('🧪 [Test] HenAPI test failed:', error);
    
    testResults.henApiTest = {
      error: error instanceof Error ? error.message : 'Unknown error',
      errorType: error instanceof Error ? error.name : 'Unknown',
      success: false
    };
  }

  return NextResponse.json(testResults);
}

// Test POST request with actual API call
export async function POST(request: NextRequest) {
  const body = await request.json();
  const { testApiCall = false } = body;

  if (!testApiCall) {
    return NextResponse.json({
      error: 'Set testApiCall: true to test actual API call'
    });
  }

  const apiKey = process.env.HENAPI_API_KEY;
  if (!apiKey) {
    return NextResponse.json({
      error: 'HENAPI_API_KEY not configured'
    });
  }

  try {
    console.log('🧪 [Test] Testing actual HenAPI call...');
    
    const henApiUrl = process.env.HENAPI_BASE_URL || "https://www.henapi.top/v1";
    const response = await fetch(`${henApiUrl}/images/generations`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: "dall-e-3",
        prompt: "A simple test image of a red rose",
        n: 1,
        size: "1024x1024"
      }),
      signal: AbortSignal.timeout(30000) // 30 second timeout
    });

    const responseText = await response.text();
    
    const result = {
      success: response.ok,
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries()),
      responseLength: responseText.length,
      response: response.ok ? JSON.parse(responseText) : responseText
    };

    console.log('🧪 [Test] Actual API call result:', {
      success: result.success,
      status: result.status,
      responseLength: result.responseLength
    });

    return NextResponse.json(result);

  } catch (error) {
    console.log('🧪 [Test] Actual API call failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      errorType: error instanceof Error ? error.name : 'Unknown'
    });
  }
}
