import { NextRequest, NextResponse } from "next/server";
import { quotaManager, getQuotaStatus, QuotaStatus, QUOTA_CONSTANTS } from "@/lib/quota-manager";

// 配额状态查询API
export async function GET(request: NextRequest) {
  try {
    const status = quotaManager.getQuotaStatus();
    const stats = quotaManager.getUsageStats();
    const quotaStatus = getQuotaStatus(status.current);
    
    // 预测不同操作的配额消耗
    const faceSwapPrediction = quotaManager.predictQuotaUsage(1, true);
    const standardPrediction = quotaManager.predictQuotaUsage(1, false);
    
    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      quota: {
        current: status.current,
        lastUpdated: status.lastUpdated,
        status: quotaStatus,
        statusDescription: getStatusDescription(quotaStatus)
      },
      costs: {
        faceSwap: QUOTA_CONSTANTS.FACE_SWAP_COST,
        standard: QUOTA_CONSTANTS.STANDARD_COST,
        minWarning: QUOTA_CONSTANTS.MIN_QUOTA_WARNING
      },
      predictions: {
        faceSwap: faceSwapPrediction,
        standard: standardPrediction
      },
      usage: stats,
      recommendations: generateRecommendations(status.current, stats),
      recentActivity: status.recentUsage.slice(-5) // 最近5条记录
    });
    
  } catch (error) {
    console.error('Error getting quota status:', error);
    return NextResponse.json({
      success: false,
      error: "Failed to get quota status",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}

// 配额管理操作API
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, quota } = body;
    
    switch (action) {
      case 'update_quota':
        if (typeof quota === 'number') {
          quotaManager.updateQuota(quota);
          return NextResponse.json({
            success: true,
            message: `Quota updated to ${quota}`,
            newStatus: quotaManager.getQuotaStatus()
          });
        } else {
          return NextResponse.json({
            success: false,
            error: "Invalid quota value"
          }, { status: 400 });
        }
        
      case 'generate_report':
        const report = quotaManager.generateReport();
        return NextResponse.json({
          success: true,
          report,
          timestamp: new Date().toISOString()
        });
        
      case 'clear_history':
        // 注意：这里需要在QuotaManager中添加clearHistory方法
        return NextResponse.json({
          success: true,
          message: "History cleared (feature not implemented yet)"
        });
        
      default:
        return NextResponse.json({
          success: false,
          error: "Unknown action",
          supportedActions: ['update_quota', 'generate_report', 'clear_history']
        }, { status: 400 });
    }
    
  } catch (error) {
    console.error('Error in quota management:', error);
    return NextResponse.json({
      success: false,
      error: "Failed to process quota management request",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}

function getStatusDescription(status: QuotaStatus): string {
  switch (status) {
    case QuotaStatus.SUFFICIENT:
      return "配额充足，可以使用人脸替换功能";
    case QuotaStatus.LOW:
      return "配额较低，建议使用标准生成模式";
    case QuotaStatus.INSUFFICIENT:
      return "配额不足，无法进行图片生成";
    case QuotaStatus.UNKNOWN:
    default:
      return "配额状态未知";
  }
}

function generateRecommendations(currentQuota: number, stats: any): string[] {
  const recommendations: string[] = [];
  
  if (currentQuota < QUOTA_CONSTANTS.FACE_SWAP_COST) {
    recommendations.push("当前配额不足以使用人脸替换功能，建议使用标准生成模式");
  }
  
  if (currentQuota < QUOTA_CONSTANTS.MIN_QUOTA_WARNING) {
    recommendations.push("配额严重不足，建议尽快充值");
  }
  
  if (stats.fallbackRate > 50) {
    recommendations.push("回退模式使用频率较高，建议增加配额以获得更好的生成效果");
  }
  
  if (stats.successRate < 80) {
    recommendations.push("成功率较低，建议检查API配置或网络连接");
  }
  
  if (currentQuota >= QUOTA_CONSTANTS.FACE_SWAP_COST * 5) {
    recommendations.push("配额充足，可以放心使用人脸替换功能");
  }
  
  if (recommendations.length === 0) {
    recommendations.push("系统运行正常，无特殊建议");
  }
  
  return recommendations;
}
