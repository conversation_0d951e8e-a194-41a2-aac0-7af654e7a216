'use client';

import { useState } from 'react';

export default function TestEditPage() {
  const [image, setImage] = useState<File | null>(null);
  const [mask, setMask] = useState<File | null>(null);
  const [prompt, setPrompt] = useState('');
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!image || !prompt) {
      setError('请选择图片并输入编辑提示');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const formData = new FormData();
      formData.append('image', image);
      if (mask) {
        formData.append('mask', mask);
      }
      formData.append('prompt', prompt);
      formData.append('n', '1');
      formData.append('size', '1:1');
      formData.append('response_format', 'url');
      formData.append('model', 'gpt-4o-image');

      const response = await fetch('/api/edit-image', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '请求失败');
      }

      setResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : '未知错误');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-center mb-8">图片编辑测试</h1>
        
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                选择要编辑的图片 *
              </label>
              <input
                type="file"
                accept="image/*"
                onChange={(e) => setImage(e.target.files?.[0] || null)}
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                支持 PNG, JPEG, WebP, GIF 格式，最大 4MB
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                选择遮罩图片（可选）
              </label>
              <input
                type="file"
                accept="image/png"
                onChange={(e) => setMask(e.target.files?.[0] || null)}
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100"
              />
              <p className="text-xs text-gray-500 mt-1">
                必须是 PNG 格式，透明区域指示要编辑的位置
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                编辑提示 *
              </label>
              <textarea
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="描述你想要的编辑效果，例如：将背景改为海滩日落"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={3}
                required
              />
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              {loading ? '编辑中...' : '开始编辑'}
            </button>
          </form>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-8">
            <div className="flex">
              <div className="text-red-800">
                <strong>错误：</strong> {error}
              </div>
            </div>
          </div>
        )}

        {result && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">编辑结果</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 原图预览 */}
              <div>
                <h3 className="text-lg font-medium mb-2">原图</h3>
                {image && (
                  <img
                    src={URL.createObjectURL(image)}
                    alt="原图"
                    className="w-full h-auto rounded-lg border"
                  />
                )}
              </div>

              {/* 编辑结果 */}
              <div>
                <h3 className="text-lg font-medium mb-2">编辑结果</h3>
                {result.data && result.data.length > 0 && (
                  <div className="space-y-4">
                    {result.data.map((item: any, index: number) => (
                      <div key={index}>
                        <img
                          src={item.url}
                          alt={`编辑结果 ${index + 1}`}
                          className="w-full h-auto rounded-lg border"
                        />
                        {item.revised_prompt && (
                          <p className="text-sm text-gray-600 mt-2">
                            <strong>修订提示：</strong> {item.revised_prompt}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* 详细信息 */}
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium mb-2">详细信息</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <p><strong>请求ID：</strong> {result.requestId}</p>
                <p><strong>生成时间：</strong> {new Date(result.created * 1000).toLocaleString()}</p>
                <p><strong>生成图片数：</strong> {result.summary?.imagesGenerated || 0}</p>
                <p><strong>使用模型：</strong> {result.summary?.model}</p>
                <p><strong>图片尺寸：</strong> {result.summary?.size}</p>
                {result.usage && (
                  <p><strong>Token使用：</strong> {result.usage.total_tokens} (输入: {result.usage.input_tokens}, 输出: {result.usage.output_tokens})</p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* 使用说明 */}
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mt-8">
          <h3 className="text-lg font-semibold text-blue-800 mb-2">使用说明</h3>
          <ul className="text-blue-700 text-sm space-y-1">
            <li>• 上传要编辑的图片（支持 PNG, JPEG, WebP, GIF）</li>
            <li>• 可选择上传遮罩图片（PNG格式），透明区域指示要编辑的位置</li>
            <li>• 输入编辑提示，描述你想要的效果</li>
            <li>• 如果没有遮罩，系统会自动检测图片中的透明区域</li>
            <li>• 编辑完成后会显示原图和编辑结果的对比</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
