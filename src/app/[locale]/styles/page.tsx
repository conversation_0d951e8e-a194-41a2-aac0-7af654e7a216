"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ArrowLeft, Check } from "lucide-react";
import { useTranslations } from "next-intl";

export default function StylesPage() {
  const [selectedStyles, setSelectedStyles] = useState<string[]>([]);
  const [uploadedPhoto, setUploadedPhoto] = useState<string | null>(null);
  const router = useRouter();
  const t = useTranslations("photoAI.styles");

  useEffect(() => {
    // Get the uploaded photo from sessionStorage
    const photo = sessionStorage.getItem("uploadedPhoto");
    if (!photo) {
      // Redirect to upload if no photo
      const currentLocale = window.location.pathname.split('/')[1];
      router.push(`/${currentLocale}/upload`);
      return;
    }
    setUploadedPhoto(photo);
  }, [router]);

  const styles = t.raw("styles") as Array<{
    id: string;
    name: string;
    description: string;
  }>;

  const toggleStyle = (styleId: string) => {
    setSelectedStyles(prev => 
      prev.includes(styleId)
        ? prev.filter(id => id !== styleId)
        : [...prev, styleId]
    );
  };

  const handleGenerate = () => {
    if (selectedStyles.length === 0) {
      alert(t("selectAtLeast"));
      return;
    }

    console.log("🎯 Starting new generation, clearing old results");

    // Clear any previous generation results to ensure fresh generation
    sessionStorage.removeItem("generatedPhotos");

    // Store selected styles in sessionStorage
    sessionStorage.setItem("selectedStyles", JSON.stringify(selectedStyles));
    const currentLocale = window.location.pathname.split('/')[1];
    router.push(`/${currentLocale}/generating`);
  };

  if (!uploadedPhoto) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 mb-4">Loading...</p>
        </div>
      </div>
    );
  }

  const currentLocale = window.location.pathname.split('/')[1];

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-pink-50 py-12">
      <div className="container mx-auto px-4">
        <Link href={`/${currentLocale}/upload`} className="inline-flex items-center text-pink-500 hover:text-pink-600 mb-8">
          <ArrowLeft className="mr-2 h-4 w-4" /> {t("backToUpload")}
        </Link>

        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold mb-4">{t("title")}</h1>
            <p className="text-gray-600 text-lg">{t("subtitle")}</p>
            <p className="text-gray-500 mt-2">{t("description")}</p>
          </div>

          {/* Preview of uploaded photo */}
          <div className="flex justify-center mb-8">
            <Card className="p-4">
              <img
                src={uploadedPhoto}
                alt="Uploaded photo"
                className="w-32 h-32 object-cover rounded-lg"
              />
            </Card>
          </div>

          {/* Style selection grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {styles.map((style) => (
              <Card
                key={style.id}
                className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                  selectedStyles.includes(style.id)
                    ? "ring-2 ring-pink-500 bg-pink-50"
                    : "hover:shadow-md"
                }`}
                onClick={() => toggleStyle(style.id)}
              >
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <h3 className="text-lg font-semibold">{style.name}</h3>
                    {selectedStyles.includes(style.id) && (
                      <div className="bg-pink-500 text-white rounded-full p-1">
                        <Check className="h-4 w-4" />
                      </div>
                    )}
                  </div>
                  <p className="text-gray-600 text-sm">{style.description}</p>
                  
                  {/* Style preview image placeholder */}
                  <div className="mt-4 h-32 bg-gradient-to-br from-pink-100 to-rose-100 rounded-lg flex items-center justify-center">
                    <span className="text-pink-500 text-sm font-medium">
                      {style.name}
                    </span>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* Selected styles summary */}
          {selectedStyles.length > 0 && (
            <Card className="p-6 mb-8 bg-pink-50 border-pink-200">
              <h3 className="font-semibold mb-2">
                Selected Styles ({selectedStyles.length}):
              </h3>
              <div className="flex flex-wrap gap-2">
                {selectedStyles.map((styleId) => {
                  const style = styles.find(s => s.id === styleId);
                  return (
                    <span
                      key={styleId}
                      className="bg-pink-500 text-white px-3 py-1 rounded-full text-sm"
                    >
                      {style?.name}
                    </span>
                  );
                })}
              </div>
            </Card>
          )}

          {/* Generate button */}
          <div className="text-center">
            <Button
              className="bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white px-8 py-6 rounded-xl text-lg font-medium min-w-[200px]"
              disabled={selectedStyles.length === 0}
              onClick={handleGenerate}
            >
              {t("generatePhotos")}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
