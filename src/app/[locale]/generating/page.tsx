"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useTranslations } from "next-intl";

export default function GeneratingPage() {
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const t = useTranslations("photoAI.generating");

  // Use ref to prevent duplicate API calls
  const apiCallMadeRef = useRef(false);
  const isComponentMountedRef = useRef(true);

  const steps = t.raw("steps") as string[];

  // Function to call the generation API
  const generatePhotos = async (photoUrl: string, styles: string[]) => {
    // Prevent multiple simultaneous calls using both state and ref
    if (isGenerating || apiCallMadeRef.current) {
      console.log("⚠️ Generation already in progress or completed, skipping duplicate call", {
        isGenerating,
        apiCallMade: apiCallMadeRef.current
      });
      return;
    }

    try {
      console.log("🚀 Starting photo generation with styles:", styles);
      console.log("📋 Request details:", {
        photoUrl: photoUrl ? `${photoUrl.substring(0, 50)}...` : "missing",
        stylesCount: styles.length,
        stylesArray: styles,
        timestamp: new Date().toISOString()
      });

      // Mark API call as made to prevent duplicates
      apiCallMadeRef.current = true;
      setIsGenerating(true);
      setError(null);

      const requestBody = {
        photoUrl,
        styles,
        userId: null // You can add user ID if available
      };

      console.log("📤 Sending request to /api/generate:", requestBody);

      const response = await fetch('/api/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      console.log("📥 Received response:", {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok
      });

      const data = await response.json();
      console.log("📄 Response data:", data);

      if (!response.ok) {
        console.error("❌ API Error:", data);
        throw new Error(data.error || 'Failed to generate photos');
      }

      console.log("✅ Generation completed successfully:", {
        success: data.success,
        photosCount: data.photos?.length || 0,
        jobId: data.jobId
      });

      // Validate response data
      if (!data.photos || !Array.isArray(data.photos) || data.photos.length === 0) {
        console.error("❌ Invalid response: No photos generated");
        throw new Error('No photos were generated');
      }

      // Store the generated photos in sessionStorage
      sessionStorage.setItem("generatedPhotos", JSON.stringify(data.photos));
      console.log("💾 Stored generated photos in sessionStorage");

      // Navigate to results page
      const currentLocale = window.location.pathname.split('/')[1];
      console.log("🔄 Navigating to results page:", `/${currentLocale}/results`);
      router.push(`/${currentLocale}/results`);

    } catch (error) {
      console.error("❌ Error generating photos:", error);
      console.error("❌ Error details:", {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });

      // Reset API call flag on error to allow retry
      apiCallMadeRef.current = false;
      setError(error instanceof Error ? error.message : 'Failed to generate photos');
      setIsGenerating(false);
    }
  };

  useEffect(() => {
    // Prevent duplicate effect runs
    if (apiCallMadeRef.current) {
      console.log("⚠️ API call already made, skipping useEffect");
      return;
    }

    console.log("🔄 useEffect running, checking session data...");

    // Check if we have the required data
    const uploadedPhoto = sessionStorage.getItem("uploadedPhoto");
    const selectedStylesStr = sessionStorage.getItem("selectedStyles");
    const generatedPhotosStr = sessionStorage.getItem("generatedPhotos");

    console.log("📋 Session data check:", {
      hasPhoto: !!uploadedPhoto,
      hasStyles: !!selectedStylesStr,
      hasGeneratedPhotos: !!generatedPhotosStr,
      photoLength: uploadedPhoto?.length || 0,
      stylesData: selectedStylesStr
    });

    if (!uploadedPhoto || !selectedStylesStr) {
      console.log("❌ Missing required data, redirecting to upload");
      const currentLocale = window.location.pathname.split('/')[1];
      router.push(`/${currentLocale}/upload`);
      return;
    }

    // If photos are already generated, skip API call and go to results
    if (generatedPhotosStr) {
      console.log("✅ Photos already generated, redirecting to results");
      const currentLocale = window.location.pathname.split('/')[1];
      router.push(`/${currentLocale}/results`);
      return;
    }

    let selectedStyles: string[];
    try {
      selectedStyles = JSON.parse(selectedStylesStr);
      console.log("🎨 Parsed selected styles:", selectedStyles);

      if (!Array.isArray(selectedStyles) || selectedStyles.length === 0) {
        console.error("❌ Invalid styles data:", selectedStyles);
        const currentLocale = window.location.pathname.split('/')[1];
        router.push(`/${currentLocale}/styles`);
        return;
      }
    } catch (error) {
      console.error("❌ Error parsing styles:", error);
      const currentLocale = window.location.pathname.split('/')[1];
      router.push(`/${currentLocale}/styles`);
      return;
    }

    // Prevent multiple API calls by checking if already generating
    if (isGenerating || apiCallMadeRef.current) {
      console.log("⚠️ Already generating or API call made, skipping duplicate call");
      return;
    }

    console.log("🚀 Starting generation process...");

    // Start the real AI generation process
    let progressInterval: NodeJS.Timeout;

    // Simulate progress while API is working
    const startProgress = () => {
      progressInterval = setInterval(() => {
        setProgress((prev) => {
          const newProgress = Math.min(prev + Math.random() * 3 + 1, 95); // Slow progress up to 95%

          // Update current step based on progress
          if (newProgress >= 25 && currentStep < 1) setCurrentStep(1);
          if (newProgress >= 50 && currentStep < 2) setCurrentStep(2);
          if (newProgress >= 75 && currentStep < 3) setCurrentStep(3);

          return newProgress;
        });
      }, 500);
    };

    // Start progress simulation
    startProgress();

    // Call the real API
    generatePhotos(uploadedPhoto, selectedStyles).finally(() => {
      clearInterval(progressInterval);
      setProgress(100);
    });

    return () => {
      if (progressInterval) clearInterval(progressInterval);
      isComponentMountedRef.current = false;
    };
  }, []); // Empty dependency array to run only once

  const handleCancel = () => {
    // Clear session storage and redirect
    sessionStorage.removeItem("uploadedPhoto");
    sessionStorage.removeItem("selectedStyles");
    sessionStorage.removeItem("generatedPhotos");
    const currentLocale = window.location.pathname.split('/')[1];
    router.push(`/${currentLocale}/upload`);
  };

  const handleRetry = () => {
    console.log("🔄 Retrying generation, clearing old results");

    // Clear old results before retrying
    sessionStorage.removeItem("generatedPhotos");

    // Reset all states and refs
    setError(null);
    setProgress(0);
    setCurrentStep(0);
    setIsGenerating(false);
    apiCallMadeRef.current = false; // Reset API call flag

    const uploadedPhoto = sessionStorage.getItem("uploadedPhoto");
    const selectedStylesStr = sessionStorage.getItem("selectedStyles");

    if (uploadedPhoto && selectedStylesStr) {
      try {
        const selectedStyles = JSON.parse(selectedStylesStr);
        console.log("🔄 Retrying with styles:", selectedStyles);
        generatePhotos(uploadedPhoto, selectedStyles);
      } catch (error) {
        console.error("❌ Error parsing styles for retry:", error);
        setError("Invalid styles data");
      }
    } else {
      console.error("❌ Missing data for retry");
      setError("Missing required data for retry");
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-pink-50 flex items-center justify-center">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto text-center">
          <Card className="p-8 relative overflow-hidden">
            {/* Animated background */}
            <div className="absolute inset-0 bg-gradient-to-r from-pink-50 to-rose-50 opacity-50">
              <div className="absolute inset-0">
                {[...Array(6)].map((_, i) => (
                  <div
                    key={i}
                    className="absolute w-4 h-4 bg-pink-300 rounded-full animate-pulse"
                    style={{
                      left: `${Math.random() * 100}%`,
                      top: `${Math.random() * 100}%`,
                      animationDelay: `${Math.random() * 2}s`,
                      animationDuration: `${2 + Math.random() * 2}s`,
                    }}
                  />
                ))}
              </div>
            </div>

            {/* Content */}
            <div className="relative z-10">
              {/* AI Icon Animation */}
              <div className="mb-8">
                <div className="w-20 h-20 mx-auto bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center animate-pulse">
                  <svg
                    className="w-10 h-10 text-white animate-spin"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                </div>
              </div>

              <h1 className="text-3xl font-bold mb-4">{t("title")}</h1>
              <p className="text-gray-600 mb-2">{t("subtitle")}</p>
              <p className="text-gray-500 text-sm mb-8">
                {isGenerating ? "AI is generating your wedding photos..." : t("description")}
              </p>

              {/* Progress Bar */}
              <div className="mb-8">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-700">{t("progress")}</span>
                  <span className="text-sm font-medium text-pink-600">{Math.round(progress)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className="bg-gradient-to-r from-pink-500 to-rose-500 h-3 rounded-full transition-all duration-300 ease-out"
                    style={{ width: `${progress}%` }}
                  />
                </div>
              </div>

              {/* Current Step */}
              <div className="mb-8">
                <div className="space-y-3">
                  {steps.map((step, index) => (
                    <div
                      key={index}
                      className={`flex items-center space-x-3 p-3 rounded-lg transition-all duration-300 ${
                        index === currentStep
                          ? "bg-pink-100 text-pink-700"
                          : index < currentStep
                          ? "bg-green-100 text-green-700"
                          : "bg-gray-100 text-gray-500"
                      }`}
                    >
                      <div
                        className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                          index === currentStep
                            ? "bg-pink-500 text-white animate-pulse"
                            : index < currentStep
                            ? "bg-green-500 text-white"
                            : "bg-gray-300 text-gray-600"
                        }`}
                      >
                        {index < currentStep ? "✓" : index + 1}
                      </div>
                      <span className="text-sm font-medium">{step}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Error Message */}
              {error && (
                <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-700 text-sm font-medium mb-2">Generation Failed</p>
                  <p className="text-red-600 text-sm">{error}</p>
                  <div className="mt-3 flex space-x-2">
                    <Button
                      size="sm"
                      onClick={handleRetry}
                      className="bg-red-500 hover:bg-red-600 text-white"
                    >
                      Retry
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleCancel}
                      className="border-red-300 text-red-600 hover:bg-red-50"
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              )}

              {/* Cancel Button */}
              {!error && (
                <Button
                  variant="outline"
                  onClick={handleCancel}
                  className="border-gray-300 text-gray-600 hover:bg-gray-50"
                >
                  {t("cancel")}
                </Button>
              )}
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
