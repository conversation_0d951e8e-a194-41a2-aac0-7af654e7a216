"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  ArrowLeft,
  Download,
  Share2,
  Heart,
  Facebook,
  Instagram,
  Twitter,
  Copy,
  Check
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { useTranslations } from "next-intl";

interface GeneratedPhoto {
  id: string;
  style: string;
  imageUrl: string;
  createdAt: string;
}

export default function ResultsPage() {
  const [generatedPhotos, setGeneratedPhotos] = useState<GeneratedPhoto[]>([]);
  const [favorites, setFavorites] = useState<string[]>([]);
  const [copiedLinks, setCopiedLinks] = useState<string[]>([]);
  const router = useRouter();
  const params = useParams();
  const currentLocale = params.locale as string;
  const t = useTranslations("photoAI.results");
  const tStyles = useTranslations("photoAI.styles");

  useEffect(() => {
    console.log("📋 Results page: Checking session data");

    // Check if we have the required data
    const uploadedPhoto = sessionStorage.getItem("uploadedPhoto");
    const selectedStylesStr = sessionStorage.getItem("selectedStyles");
    const generatedPhotosStr = sessionStorage.getItem("generatedPhotos");

    console.log("📊 Session data status:", {
      hasPhoto: !!uploadedPhoto,
      hasStyles: !!selectedStylesStr,
      hasGeneratedPhotos: !!generatedPhotosStr
    });

    if (!uploadedPhoto || !selectedStylesStr) {
      console.log("❌ Missing required data, redirecting to upload");
      router.push(`/${currentLocale}/upload`);
      return;
    }

    // Try to get generated photos from API call first
    if (generatedPhotosStr) {
      try {
        const apiGeneratedPhotos = JSON.parse(generatedPhotosStr);
        console.log("✅ Using API generated photos:", apiGeneratedPhotos.length, "photos");
        setGeneratedPhotos(apiGeneratedPhotos);
        return;
      } catch (error) {
        console.error("❌ Error parsing generated photos:", error);
      }
    }

    // Fallback to mock photos if no API generated photos
    console.log("⚠️ No API generated photos found, creating fallback photos");
    const selectedStyles = JSON.parse(selectedStylesStr);
    const styles = tStyles.raw("styles") as Array<{
      id: string;
      name: string;
      description: string;
    }>;

    const mockPhotos: GeneratedPhoto[] = selectedStyles.map((styleId: string, index: number) => {
      const style = styles.find(s => s.id === styleId);
      return {
        id: `photo-${Date.now()}-${index}`,
        style: style?.name || styleId,
        imageUrl: `/placeholder.svg?height=600&width=400&text=${encodeURIComponent(style?.name || styleId)}`,
        createdAt: new Date().toISOString(),
      };
    });

    setGeneratedPhotos(mockPhotos);
  }, [router, tStyles, currentLocale]);

  const toggleFavorite = (photoId: string) => {
    setFavorites(prev => 
      prev.includes(photoId)
        ? prev.filter(id => id !== photoId)
        : [...prev, photoId]
    );
  };

  const handleDownload = async (photo: GeneratedPhoto) => {
    try {
      console.log("📥 Starting download for photo:", photo.id);

      // For OpenAI generated images, we need to fetch and download
      if (photo.imageUrl.startsWith('http')) {
        const response = await fetch(photo.imageUrl);
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `wedding-photo-${photo.style}-${photo.id}.jpg`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up the object URL
        window.URL.revokeObjectURL(url);
        console.log("✅ Download completed for photo:", photo.id);
      } else {
        // Fallback for placeholder images
        const link = document.createElement('a');
        link.href = photo.imageUrl;
        link.download = `wedding-photo-${photo.style}-${photo.id}.jpg`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      console.error("❌ Error downloading photo:", error);
      alert("Failed to download image. Please try again.");
    }
  };

  const handleCopyLink = (photoId: string) => {
    const url = `${window.location.origin}/photo/${photoId}`;
    navigator.clipboard.writeText(url);
    setCopiedLinks(prev => [...prev, photoId]);
    setTimeout(() => {
      setCopiedLinks(prev => prev.filter(id => id !== photoId));
    }, 2000);
  };

  const handleShare = (platform: string, photo: GeneratedPhoto) => {
    const url = `${window.location.origin}/photo/${photo.id}`;
    const text = `Check out my AI-generated wedding photo in ${photo.style} style!`;
    
    let shareUrl = '';
    switch (platform) {
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
        break;
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`;
        break;
      case 'instagram':
        // Instagram doesn't support direct URL sharing, so we'll copy to clipboard
        navigator.clipboard.writeText(`${text} ${url}`);
        alert('Link copied to clipboard! You can paste it in your Instagram post.');
        return;
    }
    
    if (shareUrl) {
      window.open(shareUrl, '_blank', 'width=600,height=400');
    }
  };

  if (generatedPhotos.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-white to-pink-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 mb-4">{t("noResults")}</p>
          <Link href={`/${currentLocale}/upload`}>
            <Button className="bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white">
              {t("backToUpload")}
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-pink-50 py-12">
      <div className="container mx-auto px-4">
        <Link href={`/${currentLocale}/upload`} className="inline-flex items-center text-pink-500 hover:text-pink-600 mb-8">
          <ArrowLeft className="mr-2 h-4 w-4" /> {t("backToUpload")}
        </Link>

        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-3xl font-bold mb-4">{t("title")}</h1>
            <p className="text-gray-600 text-lg">{t("subtitle")}</p>
            <p className="text-gray-500 mt-2">{t("description")}</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {generatedPhotos.map((photo) => (
              <Card key={photo.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                <div className="relative">
                  <img
                    src={photo.imageUrl}
                    alt={`${photo.style} wedding photo`}
                    className="w-full h-80 object-cover"
                  />
                  <button
                    className={`absolute top-4 right-4 p-2 rounded-full transition-colors ${
                      favorites.includes(photo.id)
                        ? "bg-pink-500 text-white"
                        : "bg-white/80 text-gray-600 hover:bg-white"
                    }`}
                    onClick={() => toggleFavorite(photo.id)}
                  >
                    <Heart className="h-5 w-5" fill={favorites.includes(photo.id) ? "currentColor" : "none"} />
                  </button>
                </div>

                <div className="p-4">
                  <h3 className="font-bold mb-3">{photo.style}</h3>
                  
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      onClick={() => handleDownload(photo)}
                      className="flex-1 bg-pink-500 hover:bg-pink-600 text-white"
                    >
                      <Download className="mr-2 h-4 w-4" />
                      {t("download")}
                    </Button>
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button size="sm" variant="outline" className="flex-1">
                          <Share2 className="mr-2 h-4 w-4" />
                          {t("share")}
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem onClick={() => handleShare('facebook', photo)}>
                          <Facebook className="mr-2 h-4 w-4" />
                          {t("facebook")}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleShare('twitter', photo)}>
                          <Twitter className="mr-2 h-4 w-4" />
                          {t("twitter")}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleShare('instagram', photo)}>
                          <Instagram className="mr-2 h-4 w-4" />
                          {t("instagram")}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleCopyLink(photo.id)}>
                          {copiedLinks.includes(photo.id) ? (
                            <>
                              <Check className="mr-2 h-4 w-4" />
                              {t("linkCopied")}
                            </>
                          ) : (
                            <>
                              <Copy className="mr-2 h-4 w-4" />
                              {t("copyLink")}
                            </>
                          )}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          <div className="text-center space-y-6">
            <Button
              className="bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white px-8 py-6 rounded-xl text-lg font-medium"
              onClick={() => {
                router.push(`/${currentLocale}/upload`);
              }}
            >
              {t("createMore")}
            </Button>

            <p className="text-sm text-gray-500">
              {t("storageNote")}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
