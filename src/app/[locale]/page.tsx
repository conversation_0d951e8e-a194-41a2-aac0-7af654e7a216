
import { getLandingPage } from "@/app/actions";
import { unstable_setRequestLocale } from 'next-intl/server';
import {routing} from '@/i18n/routing';
import { Footer } from "@/components/ui/footer-section";
import GoogleOneTapWrapper from "@/components/GoogleOneTapWrapper";

type Locale = (typeof routing.locales)[number];

export default async function LandingPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  // 使用 await 获取 locale
  const { locale } = await params;

  // 设置请求的 locale
  unstable_setRequestLocale(locale);

  // 获取页面数据
  const page = await getLandingPage(locale);

  return (
    <>
      {/* Google One Tap组件 */}
      <GoogleOneTapWrapper />
      
      
      {/* {page.footer && <Footerdemo footer={page.footer} />} */}
      <Footer />
    </>
  );
}
