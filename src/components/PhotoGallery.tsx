"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Heart, Download, Share2, X, ZoomIn, Filter, Grid, List, ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";
import { usePathname } from "next/navigation";

interface Photo {
  id: string;
  style: string;
  imageUrl: string;
  description: string;
}

interface PhotoGalleryProps {
  photos: Photo[];
  title?: string;
  subtitle?: string;
}

export function PhotoGallery({ photos, title, subtitle }: PhotoGalleryProps) {
  const pathname = usePathname();
  const currentLocale = pathname.split('/')[1];
  const [favorites, setFavorites] = useState<string[]>([]);
  const [selectedPhoto, setSelectedPhoto] = useState<Photo | null>(null);
  const [selectedPhotoIndex, setSelectedPhotoIndex] = useState<number>(0);
  const [viewMode, setViewMode] = useState<'grid' | 'masonry'>('grid');
  const [filterStyle, setFilterStyle] = useState<string>('all');
  const [isLoading, setIsLoading] = useState<{ [key: string]: boolean }>({});

  // Get unique styles for filtering
  const uniqueStyles = ['all', ...Array.from(new Set(photos.map(photo => photo.style)))];

  // Filter photos based on selected style
  const filteredPhotos = filterStyle === 'all'
    ? photos
    : photos.filter(photo => photo.style === filterStyle);

  const toggleFavorite = (photoId: string) => {
    setFavorites(prev =>
      prev.includes(photoId)
        ? prev.filter(id => id !== photoId)
        : [...prev, photoId]
    );
  };

  const handleDownload = async (photo: Photo) => {
    setIsLoading(prev => ({ ...prev, [photo.id]: true }));
    try {
      const response = await fetch(photo.imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `wedding-photo-${photo.style}-${photo.id}.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download failed:', error);
    } finally {
      setIsLoading(prev => ({ ...prev, [photo.id]: false }));
    }
  };

  const handleShare = async (photo: Photo) => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Beautiful ${photo.style} Wedding Photo`,
          text: photo.description,
          url: window.location.href,
        });
      } catch (error) {
        console.error('Share failed:', error);
      }
    } else {
      // Fallback to copying URL
      await navigator.clipboard.writeText(window.location.href);
      // You might want to show a toast notification here
    }
  };

  const openLightbox = (photo: Photo) => {
    const index = filteredPhotos.findIndex(p => p.id === photo.id);
    setSelectedPhotoIndex(index);
    setSelectedPhoto(photo);
  };

  const navigatePhoto = (direction: 'prev' | 'next') => {
    const newIndex = direction === 'prev'
      ? (selectedPhotoIndex - 1 + filteredPhotos.length) % filteredPhotos.length
      : (selectedPhotoIndex + 1) % filteredPhotos.length;

    setSelectedPhotoIndex(newIndex);
    setSelectedPhoto(filteredPhotos[newIndex]);
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!selectedPhoto) return;

      switch (e.key) {
        case 'Escape':
          setSelectedPhoto(null);
          break;
        case 'ArrowLeft':
          navigatePhoto('prev');
          break;
        case 'ArrowRight':
          navigatePhoto('next');
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedPhoto, selectedPhotoIndex]);

  return (
    <div className="w-full">
      {/* Header Section */}
      {(title || subtitle) && (
        <div className="text-center mb-12">
          {title && (
            <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-pink-600 via-rose-600 to-purple-600 bg-clip-text text-transparent">
              {title}
            </h2>
          )}
          {subtitle && (
            <p className="text-gray-600 dark:text-gray-300 text-lg max-w-2xl mx-auto leading-relaxed">
              {subtitle}
            </p>
          )}
        </div>
      )}

      {/* Filter and View Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-center mb-8 gap-4">
        {/* Style Filter */}
        <div className="flex items-center gap-2 flex-wrap">
          <Filter className="w-4 h-4 text-gray-500" />
          <span className="text-sm text-gray-600 dark:text-gray-400 mr-2">
            {currentLocale === 'zh' ? '风格筛选:' : 'Filter by style:'}
          </span>
          {uniqueStyles.map((style) => (
            <button
              key={style}
              onClick={() => setFilterStyle(style)}
              className={`px-3 py-1 rounded-full text-sm font-medium transition-all ${
                filterStyle === style
                  ? 'bg-gradient-to-r from-pink-500 to-rose-500 text-white shadow-md'
                  : 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
              }`}
            >
              {style === 'all'
                ? (currentLocale === 'zh' ? '全部' : 'All')
                : style
              }
            </button>
          ))}
        </div>

        {/* View Mode Toggle */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600 dark:text-gray-400 mr-2">
            {currentLocale === 'zh' ? '视图:' : 'View:'}
          </span>
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded-lg transition-all ${
              viewMode === 'grid'
                ? 'bg-pink-500 text-white'
                : 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
            }`}
          >
            <Grid className="w-4 h-4" />
          </button>
          <button
            onClick={() => setViewMode('masonry')}
            className={`p-2 rounded-lg transition-all ${
              viewMode === 'masonry'
                ? 'bg-pink-500 text-white'
                : 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
            }`}
          >
            <List className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Photo Grid */}
      <div className={`grid gap-6 ${
        viewMode === 'grid'
          ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
          : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
      }`}>
        {filteredPhotos.map((photo, index) => (
          <Card
            key={photo.id}
            className={`overflow-hidden hover:shadow-xl transition-all duration-300 group cursor-pointer transform hover:scale-105 ${
              viewMode === 'masonry' ? 'h-fit' : ''
            }`}
            onClick={() => openLightbox(photo)}
          >
            <div className="relative">
              <div className={`relative ${viewMode === 'grid' ? 'h-64' : 'h-48'} overflow-hidden`}>
                <Image
                  src={photo.imageUrl}
                  alt={`${photo.style} wedding photo`}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-110"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
              </div>

              {/* Overlay with actions */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-end justify-between p-4">
                <div className="text-white">
                  <h3 className="font-semibold text-lg">{photo.style}</h3>
                  <p className="text-sm text-gray-200 line-clamp-2">{photo.description}</p>
                </div>

                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleFavorite(photo.id);
                    }}
                    className={`backdrop-blur-sm ${
                      favorites.includes(photo.id)
                        ? "bg-pink-500/80 text-white hover:bg-pink-600/80"
                        : "bg-white/80 text-gray-700 hover:bg-white/90"
                    }`}
                  >
                    <Heart className="h-4 w-4" fill={favorites.includes(photo.id) ? "currentColor" : "none"} />
                  </Button>
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDownload(photo);
                    }}
                    disabled={isLoading[photo.id]}
                    className="bg-white/80 text-gray-700 hover:bg-white/90 backdrop-blur-sm"
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleShare(photo);
                    }}
                    className="bg-white/80 text-gray-700 hover:bg-white/90 backdrop-blur-sm"
                  >
                    <Share2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Zoom indicator */}
              <div className="absolute top-4 right-4 bg-black/50 backdrop-blur-sm rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <ZoomIn className="w-4 h-4 text-white" />
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Enhanced Lightbox Modal */}
      {selectedPhoto && (
        <div
          className="fixed inset-0 bg-black/90 backdrop-blur-sm flex items-center justify-center z-50 p-4"
          onClick={() => setSelectedPhoto(null)}
        >
          <div className="max-w-6xl max-h-full relative" onClick={(e) => e.stopPropagation()}>
            {/* Navigation Arrows */}
            {filteredPhotos.length > 1 && (
              <>
                <button
                  onClick={() => navigatePhoto('prev')}
                  className="absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-black/50 backdrop-blur-sm text-white rounded-full p-3 hover:bg-black/70 transition-all"
                >
                  <ChevronLeft className="w-6 h-6" />
                </button>
                <button
                  onClick={() => navigatePhoto('next')}
                  className="absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-black/50 backdrop-blur-sm text-white rounded-full p-3 hover:bg-black/70 transition-all"
                >
                  <ChevronRight className="w-6 h-6" />
                </button>
              </>
            )}

            {/* Close Button */}
            <button
              className="absolute top-4 right-4 z-10 text-white bg-black/50 backdrop-blur-sm rounded-full p-2 hover:bg-black/70 transition-all"
              onClick={() => setSelectedPhoto(null)}
            >
              <X className="w-6 h-6" />
            </button>

            {/* Image */}
            <div className="relative max-w-full max-h-[80vh]">
              <Image
                src={selectedPhoto.imageUrl}
                alt={`${selectedPhoto.style} wedding photo`}
                width={1200}
                height={800}
                className="max-w-full max-h-full object-contain"
                priority
              />
            </div>

            {/* Photo Info */}
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6 text-white">
              <div className="flex justify-between items-end">
                <div>
                  <h3 className="text-2xl font-semibold mb-2">{selectedPhoto.style}</h3>
                  <p className="text-gray-200 text-lg">{selectedPhoto.description}</p>
                  {filteredPhotos.length > 1 && (
                    <p className="text-sm text-gray-400 mt-2">
                      {selectedPhotoIndex + 1} / {filteredPhotos.length}
                    </p>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-3">
                  <Button
                    onClick={() => toggleFavorite(selectedPhoto.id)}
                    className={`${
                      favorites.includes(selectedPhoto.id)
                        ? "bg-pink-500 hover:bg-pink-600"
                        : "bg-white/20 hover:bg-white/30"
                    } backdrop-blur-sm`}
                  >
                    <Heart className="h-4 w-4 mr-2" fill={favorites.includes(selectedPhoto.id) ? "currentColor" : "none"} />
                    {favorites.includes(selectedPhoto.id)
                      ? (currentLocale === 'zh' ? '已收藏' : 'Favorited')
                      : (currentLocale === 'zh' ? '收藏' : 'Favorite')
                    }
                  </Button>
                  <Button
                    onClick={() => handleDownload(selectedPhoto)}
                    disabled={isLoading[selectedPhoto.id]}
                    className="bg-white/20 hover:bg-white/30 backdrop-blur-sm"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    {currentLocale === 'zh' ? '下载' : 'Download'}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
