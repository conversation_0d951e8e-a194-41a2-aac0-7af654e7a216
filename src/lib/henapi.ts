// HenAPI service for image generation
// Provides integration with https://www.henapi.top/v1/images/generations

// Ensure fetch is available in the environment
// Next.js provides fetch polyfill in API routes

interface HenAPIGenerationRequest {
  model: string;
  prompt: string;
  n: number;
  size: string;
  // 添加图片输入支持，用于人脸替换或风格转换
  image?: string; // base64 encoded image or image URL
  image_url?: string; // 图片URL
  // 人脸替换相关参数
  face_swap?: boolean; // 是否启用人脸替换
  style_transfer?: boolean; // 是否启用风格转换
  // 控制参数
  strength?: number; // 生成强度 (0.0-1.0)
  guidance_scale?: number; // 引导比例
}

interface HenAPIGenerationResponse {
  data: Array<{
    url: string;
    revised_prompt?: string; // 修订后的提示词
    b64_json?: string; // base64编码的图片数据
  }>;
  created?: number; // 创建时间戳
  usage?: {
    prompt_tokens?: number;
    completion_tokens?: number;
    total_tokens?: number;
  };
}

export class HenAPIService {
  private apiKey: string;
  private baseURL: string;

  constructor(apiKey: string, baseURL: string = "https://www.henapi.top/v1") {
    this.apiKey = apiKey;
    this.baseURL = baseURL;
  }

  async generateImage(params: HenAPIGenerationRequest): Promise<HenAPIGenerationResponse> {
    const headers = new Headers();
    headers.append("Authorization", `Bearer ${this.apiKey}`);
    headers.append("Content-Type", "application/json");

    // 构建请求体，包含所有可能的参数
    const requestBodyData: any = {
      model: params.model,
      prompt: params.prompt,
      n: params.n,
      size: params.size
    };

    // 添加可选的图片相关参数
    if (params.image) {
      requestBodyData.image = params.image;
    }
    if (params.image_url) {
      requestBodyData.image_url = params.image_url;
    }
    if (params.face_swap !== undefined) {
      requestBodyData.face_swap = params.face_swap;
    }
    if (params.style_transfer !== undefined) {
      requestBodyData.style_transfer = params.style_transfer;
    }
    if (params.strength !== undefined) {
      requestBodyData.strength = params.strength;
    }
    if (params.guidance_scale !== undefined) {
      requestBodyData.guidance_scale = params.guidance_scale;
    }

    const requestBody = JSON.stringify(requestBodyData);

    const requestOptions: RequestInit = {
      method: 'POST',
      headers: headers,
      body: requestBody,
      redirect: 'follow',
      // 添加超时设置
      signal: AbortSignal.timeout(60000) // 60秒超时
    };

    const endpoint = `${this.baseURL}/images/generations`;

    // 详细的请求日志
    console.log('🔍 [HenAPI] Request Details:');
    console.log('  📍 Endpoint:', endpoint);
    console.log('  🔑 API Key:', this.apiKey ? `${this.apiKey.substring(0, 8)}...${this.apiKey.slice(-4)}` : 'NOT_SET');
    console.log('  📋 Headers:', {
      'Authorization': `Bearer ${this.apiKey ? this.apiKey.substring(0, 8) + '...' + this.apiKey.slice(-4) : 'NOT_SET'}`,
      'Content-Type': 'application/json'
    });
    console.log('  📦 Request Body:', {
      model: params.model,
      prompt: params.prompt.substring(0, 100) + (params.prompt.length > 100 ? '...' : ''),
      n: params.n,
      size: params.size,
      hasImage: !!params.image,
      hasImageUrl: !!params.image_url,
      faceSwap: params.face_swap,
      styleTransfer: params.style_transfer,
      strength: params.strength,
      guidanceScale: params.guidance_scale,
      imagePreview: params.image ? `${params.image.substring(0, 50)}...` : 'N/A',
      imageUrlPreview: params.image_url ? `${params.image_url.substring(0, 80)}...` : 'N/A'
    });

    try {
      // Check if fetch is available
      if (typeof fetch === 'undefined') {
        throw new Error('fetch is not available in this environment. Please ensure you are running in a Next.js API route or upgrade to Node.js 18+');
      }

      console.log('🚀 [HenAPI] Sending request to:', endpoint);
      const startTime = Date.now();

      const response = await fetch(endpoint, requestOptions);

      const duration = Date.now() - startTime;
      console.log(`⏱️ [HenAPI] Request completed in ${duration}ms`);
      console.log('📥 [HenAPI] Response Status:', response.status, response.statusText);
      console.log('📥 [HenAPI] Response Headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorText = await response.text();
        console.log('❌ [HenAPI] Error Response Body:', errorText);
        throw new Error(`HenAPI request failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const result = await response.json();
      console.log('✅ [HenAPI] Success Response:', {
        created: result.created,
        dataLength: result.data?.length || 0,
        hasImageUrl: !!(result.data?.[0]?.url),
        imageUrlPreview: result.data?.[0]?.url ? result.data[0].url.substring(0, 80) + "..." : "N/A",
        hasRevisedPrompt: !!(result.data?.[0]?.revised_prompt),
        revisedPromptPreview: result.data?.[0]?.revised_prompt ? result.data[0].revised_prompt.substring(0, 100) + "..." : "N/A"
      });

      return result as HenAPIGenerationResponse;
    } catch (error) {
      console.log('❌ [HenAPI] Request failed with error:', error);

      if (error instanceof Error) {
        console.log('❌ [HenAPI] Error details:', {
          name: error.name,
          message: error.message,
          stack: error.stack?.substring(0, 500)
        });

        // 提供更详细的错误信息
        if (error.message.includes('fetch failed')) {
          console.log('💡 [HenAPI] Network error detected. Possible causes:');
          console.log('  - Network connectivity issues');
          console.log('  - DNS resolution problems');
          console.log('  - Firewall blocking the request');
          console.log('  - HenAPI service is down');
          console.log('  - SSL/TLS certificate issues');
        }

        throw new Error(`HenAPI generation failed: ${error.message}`);
      }
      throw new Error('HenAPI generation failed: Unknown error');
    }
  }
}

// Factory function to create HenAPI service instance
export function createHenAPIService(): HenAPIService | null {
  const apiKey = process.env.HENAPI_API_KEY;
  const baseURL = process.env.HENAPI_BASE_URL || "https://www.henapi.top/v1";

  if (!apiKey) {
    console.error('HENAPI_API_KEY not configured');
    return null;
  }

  return new HenAPIService(apiKey, baseURL);
}
