// 配额管理工具
// 用于监控和管理HenAPI的配额使用情况

interface QuotaInfo {
  current: number;
  required: number;
  sufficient: boolean;
  percentage: number;
}

interface QuotaUsageRecord {
  timestamp: string;
  operation: string;
  quotaUsed: number;
  quotaRemaining: number;
  success: boolean;
  fallbackUsed?: boolean;
}

export class QuotaManager {
  private static instance: QuotaManager;
  private usageHistory: QuotaUsageRecord[] = [];
  private currentQuota: number = 0;
  private lastUpdated: Date = new Date();

  private constructor() {}

  static getInstance(): QuotaManager {
    if (!QuotaManager.instance) {
      QuotaManager.instance = new QuotaManager();
    }
    return QuotaManager.instance;
  }

  // 解析配额错误信息
  parseQuotaError(errorMessage: string): QuotaInfo | null {
    const quotaMatch = errorMessage.match(/user quota: (\d+), need quota: (\d+)/);
    if (quotaMatch) {
      const current = parseInt(quotaMatch[1]);
      const required = parseInt(quotaMatch[2]);
      return {
        current,
        required,
        sufficient: current >= required,
        percentage: (current / required) * 100
      };
    }
    return null;
  }

  // 更新当前配额信息
  updateQuota(quota: number) {
    this.currentQuota = quota;
    this.lastUpdated = new Date();
  }

  // 记录配额使用
  recordUsage(operation: string, quotaUsed: number, quotaRemaining: number, success: boolean, fallbackUsed?: boolean) {
    const record: QuotaUsageRecord = {
      timestamp: new Date().toISOString(),
      operation,
      quotaUsed,
      quotaRemaining,
      success,
      fallbackUsed
    };
    
    this.usageHistory.push(record);
    this.updateQuota(quotaRemaining);
    
    // 保持最近100条记录
    if (this.usageHistory.length > 100) {
      this.usageHistory = this.usageHistory.slice(-100);
    }
  }

  // 检查是否应该使用回退模式
  shouldUseFallback(requiredQuota: number = 5000): boolean {
    return this.currentQuota < requiredQuota;
  }

  // 获取配额状态
  getQuotaStatus(): {
    current: number;
    lastUpdated: Date;
    recentUsage: QuotaUsageRecord[];
    fallbackRecommended: boolean;
  } {
    return {
      current: this.currentQuota,
      lastUpdated: this.lastUpdated,
      recentUsage: this.usageHistory.slice(-10),
      fallbackRecommended: this.shouldUseFallback()
    };
  }

  // 获取使用统计
  getUsageStats(): {
    totalOperations: number;
    successfulOperations: number;
    fallbackOperations: number;
    successRate: number;
    fallbackRate: number;
  } {
    const total = this.usageHistory.length;
    const successful = this.usageHistory.filter(r => r.success).length;
    const fallback = this.usageHistory.filter(r => r.fallbackUsed).length;

    return {
      totalOperations: total,
      successfulOperations: successful,
      fallbackOperations: fallback,
      successRate: total > 0 ? (successful / total) * 100 : 0,
      fallbackRate: total > 0 ? (fallback / total) * 100 : 0
    };
  }

  // 预测配额消耗
  predictQuotaUsage(operations: number, usesFaceSwap: boolean = true): {
    estimatedCost: number;
    canAfford: boolean;
    recommendFallback: boolean;
  } {
    const costPerOperation = usesFaceSwap ? 5000 : 3000; // 估算值
    const estimatedCost = operations * costPerOperation;
    
    return {
      estimatedCost,
      canAfford: this.currentQuota >= estimatedCost,
      recommendFallback: this.currentQuota < estimatedCost && this.currentQuota >= (operations * 3000)
    };
  }

  // 生成配额报告
  generateReport(): string {
    const status = this.getQuotaStatus();
    const stats = this.getUsageStats();
    
    return `
📊 HenAPI 配额使用报告
===================
当前配额: ${status.current}
最后更新: ${status.lastUpdated.toLocaleString()}
回退模式推荐: ${status.fallbackRecommended ? '是' : '否'}

📈 使用统计
-----------
总操作数: ${stats.totalOperations}
成功操作: ${stats.successfulOperations}
回退操作: ${stats.fallbackOperations}
成功率: ${stats.successRate.toFixed(1)}%
回退率: ${stats.fallbackRate.toFixed(1)}%

📋 最近使用记录
--------------
${status.recentUsage.map(r => 
  `${new Date(r.timestamp).toLocaleTimeString()} - ${r.operation} - ${r.success ? '成功' : '失败'}${r.fallbackUsed ? ' (回退)' : ''}`
).join('\n')}
    `.trim();
  }
}

// 导出单例实例
export const quotaManager = QuotaManager.getInstance();

// 配额相关的常量
export const QUOTA_CONSTANTS = {
  FACE_SWAP_COST: 5000,
  STANDARD_COST: 3000,
  MIN_QUOTA_WARNING: 1000,
  FALLBACK_THRESHOLD: 5000
};

// 配额状态枚举
export enum QuotaStatus {
  SUFFICIENT = 'sufficient',
  LOW = 'low',
  INSUFFICIENT = 'insufficient',
  UNKNOWN = 'unknown'
}

// 获取配额状态
export function getQuotaStatus(current: number, required: number = QUOTA_CONSTANTS.FACE_SWAP_COST): QuotaStatus {
  if (current >= required) return QuotaStatus.SUFFICIENT;
  if (current >= QUOTA_CONSTANTS.STANDARD_COST) return QuotaStatus.LOW;
  if (current < QUOTA_CONSTANTS.MIN_QUOTA_WARNING) return QuotaStatus.INSUFFICIENT;
  return QuotaStatus.UNKNOWN;
}
