// APICore service for image editing
// Provides integration with https://api.apicore.ai/v1/images/edits

// Ensure fetch is available in the environment
// Next.js provides fetch polyfill in API routes

interface APICoreImageEditRequest {
  image: File | string; // 要编辑的图像文件或base64字符串
  mask?: File | string; // 可选的遮罩图像
  prompt: string; // 编辑描述
  n?: number; // 生成图像数量，默认1
  size?: string; // 图像尺寸，支持 "1:1", "2:3", "3:2"
  response_format?: "url" | "b64_json"; // 返回格式
  user?: string; // 用户标识
  model?: string; // 模型名称
}

interface APICoreImageEditResponse {
  data: Array<{
    url?: string;
    b64_json?: string;
    revised_prompt?: string; // 修订后的提示词
  }>;
  created: number; // 创建时间戳
  usage?: {
    prompt_tokens?: number;
    completion_tokens?: number;
    total_tokens?: number;
    input_tokens?: number;
    output_tokens?: number;
    input_tokens_details?: {
      text_tokens?: number;
      image_tokens?: number;
      cached_tokens_details?: any;
    };
    output_tokens_details?: any;
    prompt_tokens_details?: {
      cached_tokens_details?: any;
    };
    completion_tokens_details?: any;
  };
}

export class APICoreService {
  private apiKey: string;
  private baseURL: string;

  constructor(apiKey: string, baseURL: string = "https://api.apicore.ai/v1") {
    this.apiKey = apiKey;
    this.baseURL = baseURL;
  }

  async editImage(params: APICoreImageEditRequest): Promise<APICoreImageEditResponse> {
    const url = `${this.baseURL}/images/edits`;
    
    console.log('🔄 APICore: Preparing image edit request');
    console.log('  🎯 Target URL:', url);
    console.log('  🔑 API Key Preview:', this.apiKey ? `${this.apiKey.substring(0, 8)}...${this.apiKey.slice(-4)}` : 'NOT_SET');

    // 创建FormData对象
    const formData = new FormData();

    // 处理图像文件
    if (typeof params.image === 'string') {
      // 如果是base64字符串，需要转换为Blob
      if (params.image.startsWith('data:image/')) {
        const response = await fetch(params.image);
        const blob = await response.blob();
        formData.append('image', blob, 'image.png');
      } else {
        // 如果是URL，需要先下载
        const response = await fetch(params.image);
        const blob = await response.blob();
        formData.append('image', blob, 'image.png');
      }
    } else {
      // 如果是File对象
      formData.append('image', params.image);
    }

    // 处理遮罩图像（可选）
    if (params.mask) {
      if (typeof params.mask === 'string') {
        if (params.mask.startsWith('data:image/')) {
          const response = await fetch(params.mask);
          const blob = await response.blob();
          formData.append('mask', blob, 'mask.png');
        } else {
          const response = await fetch(params.mask);
          const blob = await response.blob();
          formData.append('mask', blob, 'mask.png');
        }
      } else {
        formData.append('mask', params.mask);
      }
    }

    // 添加其他参数
    formData.append('prompt', params.prompt);
    
    if (params.n !== undefined) {
      formData.append('n', params.n.toString());
    }
    
    if (params.size) {
      formData.append('size', params.size);
    }
    
    if (params.response_format) {
      formData.append('response_format', params.response_format);
    }
    
    if (params.user) {
      formData.append('user', params.user);
    }
    
    if (params.model) {
      formData.append('model', params.model);
    }

    console.log('  📦 Request Parameters:', {
      prompt: params.prompt.substring(0, 100) + (params.prompt.length > 100 ? '...' : ''),
      n: params.n || 1,
      size: params.size || 'default',
      response_format: params.response_format || 'url',
      model: params.model || 'default',
      hasImage: !!params.image,
      hasMask: !!params.mask,
      user: params.user || 'anonymous'
    });

    try {
      // Check if fetch is available
      if (typeof fetch === 'undefined') {
        throw new Error('fetch is not available in this environment. Please ensure you are running in a Next.js API route or upgrade to Node.js 18+');
      }

      console.log('  🚀 Sending request to APICore...');
      
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          // 注意：不要设置Content-Type，让浏览器自动设置multipart/form-data边界
        },
        body: formData,
      });

      console.log('  📥 APICore response status:', response.status);
      console.log('  📥 APICore response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorText = await response.text();
        console.error('  ❌ APICore error response:', {
          status: response.status,
          statusText: response.statusText,
          body: errorText
        });
        
        // 尝试解析错误信息
        let errorMessage = `APICore API error: ${response.status} ${response.statusText}`;
        try {
          const errorData = JSON.parse(errorText);
          if (errorData.error) {
            errorMessage = `APICore API error: ${errorData.error}`;
          }
        } catch (parseError) {
          // 如果无法解析JSON，使用原始错误文本
          if (errorText) {
            errorMessage = `APICore API error: ${errorText}`;
          }
        }
        
        throw new Error(errorMessage);
      }

      const responseData = await response.json();
      console.log('  ✅ APICore response received successfully');
      console.log('  📊 Response data structure:', {
        hasData: !!responseData.data,
        dataLength: responseData.data?.length || 0,
        firstItemKeys: responseData.data?.[0] ? Object.keys(responseData.data[0]) : [],
        hasUsage: !!responseData.usage,
        created: responseData.created
      });

      return responseData as APICoreImageEditResponse;
    } catch (error) {
      console.error('  ❌ APICore request failed:', error);
      
      if (error instanceof Error) {
        // 重新抛出带有更多上下文的错误
        throw new Error(`APICore image edit failed: ${error.message}`);
      } else {
        throw new Error('APICore image edit failed: Unknown error');
      }
    }
  }
}

// Factory function to create APICore service instance
export function createAPICoreService(): APICoreService | null {
  const apiKey = process.env.APICORE_API_KEY;
  const baseURL = process.env.APICORE_BASE_URL || "https://api.apicore.ai/v1";

  if (!apiKey) {
    console.error('APICORE_API_KEY not configured');
    return null;
  }

  return new APICoreService(apiKey, baseURL);
}
