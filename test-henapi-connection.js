#!/usr/bin/env node

/**
 * Test HenAPI connection directly
 * This script tests the HenAPI endpoint with the correct configuration
 */

require('dotenv').config({ path: '.env.local' });

console.log("🧪 Testing HenAPI Connection");
console.log("=" .repeat(50));

async function testHenAPIConnection() {
  const apiKey = process.env.HENAPI_API_KEY;
  const baseURL = process.env.HENAPI_BASE_URL || "https://www.henapi.top/v1";
  
  console.log(`📋 Configuration:`);
  console.log(`   API Key: ${apiKey ? 'SET (***' + apiKey.slice(-4) + ')' : 'NOT_SET'}`);
  console.log(`   Base URL: ${baseURL}`);
  console.log(`   Endpoint: ${baseURL}/images/generations`);
  
  if (!apiKey) {
    console.log("❌ HENAPI_API_KEY not configured");
    return;
  }
  
  console.log("\n🔄 Testing HenAPI connection...");
  
  try {
    const headers = new Headers();
    headers.append("Authorization", `Bearer ${apiKey}`);
    headers.append("Content-Type", "application/json");

    const requestBody = JSON.stringify({
      "model": "dall-e-3",
      "prompt": "A cute baby sea otter",
      "n": 1,
      "size": "1024x1024"
    });

    const requestOptions = {
      method: 'POST',
      headers: headers,
      body: requestBody,
      redirect: 'follow'
    };

    console.log("📤 Sending request to HenAPI...");
    const response = await fetch(`${baseURL}/images/generations`, requestOptions);
    
    console.log(`📥 Response status: ${response.status} ${response.statusText}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log(`❌ Request failed: ${errorText}`);
      
      if (response.status === 401) {
        console.log("💡 This is an authentication error. Please check:");
        console.log("   - Your API key is correct");
        console.log("   - Your API key has sufficient credits");
        console.log("   - Your API key is not expired");
      } else if (response.status === 429) {
        console.log("💡 Rate limit exceeded. Please wait and try again.");
      } else if (response.status === 400) {
        console.log("💡 Bad request. Please check the request format.");
      }
      return;
    }
    
    const result = await response.json();
    console.log("✅ Request successful!");
    console.log(`📊 Response data:`, {
      created: result.created,
      dataLength: result.data?.length || 0,
      hasImageUrl: !!(result.data?.[0]?.url),
      imageUrlPreview: result.data?.[0]?.url ? result.data[0].url.substring(0, 80) + "..." : "N/A"
    });
    
    if (result.data?.[0]?.revised_prompt) {
      console.log(`🎨 Revised prompt: ${result.data[0].revised_prompt}`);
    }
    
    console.log("\n🎉 HenAPI connection test successful!");
    
  } catch (error) {
    console.log(`❌ Connection test failed: ${error.message}`);
    
    if (error.message.includes('fetch is not defined')) {
      console.log("💡 Running in Node.js environment without fetch. This is expected.");
      console.log("   The actual application will work correctly in the browser/Next.js environment.");
    } else if (error.message.includes('ENOTFOUND') || error.message.includes('ECONNREFUSED')) {
      console.log("💡 Network connection issue. Please check:");
      console.log("   - Your internet connection");
      console.log("   - The HenAPI service is available");
      console.log("   - No firewall blocking the request");
    }
  }
}

// Check if we're in a Node.js environment that supports fetch
if (typeof fetch === 'undefined') {
  console.log("⚠️  Note: This test requires a fetch-enabled environment.");
  console.log("   In Node.js 18+, fetch is available globally.");
  console.log("   In older Node.js versions, you may need to install node-fetch.");
  console.log("\n💡 The actual application will work correctly as Next.js provides fetch.");
  
  // Try to import node-fetch if available
  (async () => {
    try {
      const { default: fetch } = await import('node-fetch');
      global.fetch = fetch;
      global.Headers = (await import('node-fetch')).Headers;
      await testHenAPIConnection();
    } catch (importError) {
      console.log("\n📋 Manual test command:");
      console.log(`curl --location --request POST '${process.env.HENAPI_BASE_URL || "https://www.henapi.top/v1"}/images/generations' \\`);
      console.log(`--header 'Authorization: Bearer ${process.env.HENAPI_API_KEY || "YOUR_API_KEY"}' \\`);
      console.log(`--header 'Content-Type: application/json' \\`);
      console.log(`--data-raw '{
    "model": "dall-e-3",
    "prompt": "A cute baby sea otter",
    "n": 1,
    "size": "1024x1024"
  }'`);
    }
  })();
} else {
  testHenAPIConnection();
}

console.log("\n" + "=" .repeat(50));
