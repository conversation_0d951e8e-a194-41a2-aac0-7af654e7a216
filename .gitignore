# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

*.mdc

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage
.vscode
.idea
# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# env files (can opt-in for committing if needed)
.env
.env.local
.env.development.local
.env.test.local
.env.product

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
