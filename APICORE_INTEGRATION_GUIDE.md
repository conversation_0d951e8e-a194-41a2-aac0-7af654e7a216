# APICore Integration Guide

## 概述

本项目现在支持三种AI图片生成/编辑模式：
- **OpenAI模式**: 使用OpenAI的DALL-E 3模型进行图片生成
- **HenAPI模式**: 使用HenAPI的图片生成服务 (https://www.henapi.top/v1)
- **APICore模式**: 使用APICore的图片编辑服务 (https://api.apicore.ai/v1) - **新增功能**

APICore模式专门用于图片编辑，支持基于提示词对现有图片进行修改。

## 新增功能特性

### 图片编辑API
- **端点**: `/api/edit-image`
- **功能**: 基于提示词编辑现有图片
- **支持格式**: PNG, JPEG, WebP, GIF
- **最大文件大小**: 4MB
- **支持遮罩**: 可选的PNG遮罩图片指定编辑区域

### 测试页面
- **路径**: `/test-edit`
- **功能**: 提供图片编辑功能的可视化测试界面
- **特性**: 原图与编辑结果对比显示

## 配置步骤

### 1. 环境变量配置

复制 `.env.example` 到 `.env.local`:
```bash
cp .env.example .env.local
```

### 2. 配置APICore模式

在 `.env.local` 中添加APICore配置：
```env
# 图片生成模式选择
AI_GENERATION_MODE=apicore

# APICore配置
APICORE_API_KEY=your_apicore_key_here
APICORE_BASE_URL=https://api.apicore.ai/v1

# 其他模式配置（可选，作为备用）
OPENAI_API_KEY=your_openai_key_here
OPENAI_BASE_URL=https://api.laozhang.ai/v1

HENAPI_API_KEY=your_henapi_key_here
HENAPI_BASE_URL=https://www.henapi.top/v1
```

### 3. 获取APICore API密钥

1. 访问 [APICore官网](https://doc.apicore.ai/)
2. 注册账户并获取API密钥
3. 将密钥配置到环境变量中

## API使用方法

### 图片编辑API

**端点**: `POST /api/edit-image`

**请求格式**: `multipart/form-data`

**必需参数**:
- `image`: 要编辑的图片文件
- `prompt`: 编辑描述文本

**可选参数**:
- `mask`: PNG格式的遮罩图片
- `n`: 生成图片数量 (默认: 1)
- `size`: 图片尺寸 ("1:1", "2:3", "3:2")
- `response_format`: 返回格式 ("url" 或 "b64_json")
- `user`: 用户标识
- `model`: 模型名称 (默认: "gpt-4o-image")

**示例请求**:
```javascript
const formData = new FormData();
formData.append('image', imageFile);
formData.append('prompt', '将背景改为海滩日落');
formData.append('n', '1');
formData.append('size', '1:1');

const response = await fetch('/api/edit-image', {
  method: 'POST',
  body: formData,
});

const result = await response.json();
```

**响应格式**:
```json
{
  "success": true,
  "requestId": "edit-1234567890-abc123",
  "data": [
    {
      "url": "https://example.com/edited-image.png",
      "revised_prompt": "修订后的提示词"
    }
  ],
  "created": 1234567890,
  "usage": {
    "total_tokens": 1000,
    "input_tokens": 500,
    "output_tokens": 500
  },
  "summary": {
    "imagesGenerated": 1,
    "prompt": "将背景改为海滩日落",
    "model": "gpt-4o-image",
    "size": "1:1"
  }
}
```

### 配置查询API

**端点**: `GET /api/generate`

查看当前配置状态：
```json
{
  "message": "Wedding photo generation API",
  "currentMode": "apicore",
  "supportedModes": ["openai", "henapi", "apicore"],
  "configuration": {
    "mode": "apicore",
    "hasOpenAIKey": true,
    "hasHenAPIKey": true,
    "hasAPICoreKey": true,
    "openaiBaseURL": "https://api.laozhang.ai/v1",
    "henApiBaseURL": "https://www.henapi.top/v1",
    "apicoreBaseURL": "https://api.apicore.ai/v1"
  }
}
```

## 使用流程

### 1. 基本图片编辑
1. 配置APICore API密钥
2. 启动开发服务器: `pnpm dev`
3. 访问测试页面: `http://localhost:3000/test-edit`
4. 上传图片并输入编辑提示
5. 查看编辑结果

### 2. 高级编辑（使用遮罩）
1. 准备原图和PNG格式的遮罩图片
2. 遮罩图片中的透明区域指示要编辑的位置
3. 上传两个文件并输入编辑提示
4. 系统将只编辑遮罩指定的区域

### 3. 程序化调用
```javascript
// 使用APICore服务
import { createAPICoreService } from '@/lib/apicore';

const apicoreService = createAPICoreService();
if (apicoreService) {
  const result = await apicoreService.editImage({
    image: imageFile,
    prompt: "将背景改为星空",
    n: 1,
    size: "1:1",
    response_format: "url"
  });
  console.log('编辑结果:', result.data[0].url);
}
```

## 错误处理

常见错误及解决方案：

1. **API密钥错误**
   - 检查 `APICORE_API_KEY` 是否正确配置
   - 确认API密钥有效且有足够配额

2. **文件格式错误**
   - 确保图片格式为 PNG, JPEG, WebP 或 GIF
   - 遮罩必须为PNG格式

3. **文件大小超限**
   - 图片文件不能超过4MB
   - 压缩图片后重试

4. **提示词违规**
   - 修改提示词内容，避免违反内容政策

## 技术实现

### 文件结构
```
src/
├── lib/
│   └── apicore.ts          # APICore服务实现
├── app/
│   ├── api/
│   │   └── edit-image/
│   │       └── route.ts    # 图片编辑API路由
│   └── test-edit/
│       └── page.tsx        # 测试页面
└── .env.example            # 环境变量示例
```

### 核心组件

1. **APICoreService类** (`src/lib/apicore.ts`):
   - 封装APICore API调用逻辑
   - 处理文件上传和FormData构建
   - 提供类型安全的接口

2. **图片编辑API** (`src/app/api/edit-image/route.ts`):
   - 处理multipart/form-data请求
   - 文件验证和错误处理
   - 详细的日志记录

3. **测试界面** (`src/app/test-edit/page.tsx`):
   - 可视化的图片编辑测试工具
   - 原图与结果对比显示
   - 详细的使用说明

## 注意事项

1. **API配额**: APICore是付费服务，请注意API调用配额
2. **文件安全**: 上传的图片会发送到APICore服务器处理
3. **网络延迟**: 图片编辑可能需要几秒到几分钟时间
4. **内容政策**: 遵守APICore的内容使用政策

## 故障排除

### 检查配置
```bash
# 检查环境变量
echo $APICORE_API_KEY
echo $APICORE_BASE_URL

# 测试API连接
curl -X GET http://localhost:3000/api/edit-image
```

### 查看日志
开发模式下，所有API调用都会在控制台输出详细日志，包括：
- 请求参数
- 响应状态
- 错误信息
- 性能指标

## 更新日志

### v1.0.0 (2025-01-25)
- ✅ 新增APICore图片编辑服务
- ✅ 新增 `/api/edit-image` API端点
- ✅ 新增图片编辑测试页面
- ✅ 更新环境配置支持APICore
- ✅ 完善错误处理和日志记录
