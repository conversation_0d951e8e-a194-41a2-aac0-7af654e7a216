#!/usr/bin/env node

// APICore使用演示脚本
// 展示如何使用新增的图片编辑功能

console.log('🎨 APICore图片编辑功能演示');
console.log('=====================================\n');

console.log('📋 功能概述:');
console.log('- 基于提示词编辑现有图片');
console.log('- 支持可选的遮罩图片指定编辑区域');
console.log('- 支持多种图片格式 (PNG, JPEG, WebP, GIF)');
console.log('- 最大文件大小: 4MB');
console.log('- API端点: /api/edit-image\n');

console.log('🔧 配置要求:');
console.log('1. 设置环境变量:');
console.log('   AI_GENERATION_MODE=apicore');
console.log('   APICORE_API_KEY=your_api_key_here');
console.log('   APICORE_BASE_URL=https://api.apicore.ai/v1\n');

console.log('2. 获取APICore API密钥:');
console.log('   - 访问: https://doc.apicore.ai/');
console.log('   - 注册账户并获取API密钥');
console.log('   - 将密钥添加到 .env.local 文件\n');

console.log('🌐 API使用示例:');

console.log('\n📤 前端JavaScript示例:');
console.log(`// 基本图片编辑
const formData = new FormData();
formData.append('image', imageFile);
formData.append('prompt', '将背景改为海滩日落');
formData.append('n', '1');
formData.append('size', '1:1');
formData.append('response_format', 'url');

const response = await fetch('/api/edit-image', {
  method: 'POST',
  body: formData,
});

const result = await response.json();
if (result.success) {
  console.log('编辑结果:', result.data[0].url);
}`);

console.log('\n📤 使用遮罩的高级编辑:');
console.log(`// 使用遮罩指定编辑区域
const formData = new FormData();
formData.append('image', originalImageFile);
formData.append('mask', maskImageFile); // PNG格式，透明区域指示编辑位置
formData.append('prompt', '在指定区域添加一朵花');
formData.append('n', '1');
formData.append('size', '1:1');

const response = await fetch('/api/edit-image', {
  method: 'POST',
  body: formData,
});`);

console.log('\n📤 cURL命令示例:');
console.log(`curl -X POST http://localhost:3000/api/edit-image \\
  -F "image=@/path/to/your/image.jpg" \\
  -F "prompt=将背景改为星空" \\
  -F "n=1" \\
  -F "size=1:1" \\
  -F "response_format=url" \\
  -F "model=gpt-4o-image"`);

console.log('\n📤 带遮罩的cURL示例:');
console.log(`curl -X POST http://localhost:3000/api/edit-image \\
  -F "image=@/path/to/original.jpg" \\
  -F "mask=@/path/to/mask.png" \\
  -F "prompt=在遮罩区域添加彩虹" \\
  -F "n=1" \\
  -F "size=1:1"`);

console.log('\n🔧 后端服务示例:');
console.log(`// 使用APICore服务直接调用
import { createAPICoreService } from '@/lib/apicore';

const apicoreService = createAPICoreService();
if (apicoreService) {
  const result = await apicoreService.editImage({
    image: imageFile,
    prompt: "将背景改为森林",
    n: 1,
    size: "1:1",
    response_format: "url"
  });
  console.log('编辑结果:', result.data[0].url);
}`);

console.log('\n📱 测试界面使用:');
console.log('1. 启动开发服务器: pnpm dev');
console.log('2. 访问测试页面: http://localhost:3000/test-edit');
console.log('3. 上传图片文件');
console.log('4. 输入编辑提示词');
console.log('5. 可选择上传遮罩图片');
console.log('6. 点击"开始编辑"按钮');
console.log('7. 查看原图与编辑结果对比\n');

console.log('📊 响应格式示例:');
console.log(`{
  "success": true,
  "requestId": "edit-1234567890-abc123",
  "data": [
    {
      "url": "https://example.com/edited-image.png",
      "revised_prompt": "修订后的提示词"
    }
  ],
  "created": 1234567890,
  "usage": {
    "total_tokens": 1000,
    "input_tokens": 500,
    "output_tokens": 500
  },
  "summary": {
    "imagesGenerated": 1,
    "prompt": "将背景改为海滩日落",
    "model": "gpt-4o-image",
    "size": "1:1"
  }
}`);

console.log('\n⚠️ 注意事项:');
console.log('1. APICore是付费服务，请注意API调用配额');
console.log('2. 图片文件大小不能超过4MB');
console.log('3. 遮罩文件必须是PNG格式');
console.log('4. 透明区域指示要编辑的位置');
console.log('5. 编辑过程可能需要几秒到几分钟时间');
console.log('6. 遵守APICore的内容使用政策\n');

console.log('🔍 常见错误及解决方案:');
console.log('1. "API configuration error: Missing APICore API key"');
console.log('   → 检查 APICORE_API_KEY 环境变量是否正确设置\n');

console.log('2. "Unsupported image type"');
console.log('   → 确保图片格式为 PNG, JPEG, WebP 或 GIF\n');

console.log('3. "Image too large"');
console.log('   → 压缩图片文件至4MB以下\n');

console.log('4. "Mask must be PNG format"');
console.log('   → 确保遮罩文件为PNG格式\n');

console.log('5. "Content policy violation"');
console.log('   → 修改提示词内容，避免违反内容政策\n');

console.log('📚 相关文档:');
console.log('- APICore集成指南: APICORE_INTEGRATION_GUIDE.md');
console.log('- APICore官方文档: https://doc.apicore.ai/api-301177868');
console.log('- 项目环境配置: .env.example\n');

console.log('🚀 快速开始:');
console.log('1. 复制环境变量: cp .env.example .env.local');
console.log('2. 编辑 .env.local，设置 APICore 配置');
console.log('3. 启动服务器: pnpm dev');
console.log('4. 访问测试页面: http://localhost:3000/test-edit');
console.log('5. 开始编辑图片！\n');

console.log('✨ 享受使用APICore的图片编辑功能！');
