#!/usr/bin/env python3
"""
Simple HTTP server for testing the AI Wedding Dress Studio locally.
Run this script and open http://localhost:8000 in your browser.
"""

import http.server
import socketserver
import webbrowser
import os
import sys

PORT = 8000

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # Add CORS headers for local development
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

def main():
    # Change to the directory containing this script
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        print(f"🚀 AI Wedding Dress Studio Server")
        print(f"📍 Serving at http://localhost:{PORT}")
        print(f"📱 Demo page: http://localhost:{PORT}/demo.html")
        print(f"🎭 Main app: http://localhost:{PORT}/index.html")
        print(f"⏹️  Press Ctrl+C to stop the server")
        print("-" * 50)
        
        # Try to open the browser automatically
        try:
            webbrowser.open(f'http://localhost:{PORT}/demo.html')
            print("🌐 Opening browser automatically...")
        except:
            print("💡 Please open http://localhost:{PORT}/demo.html in your browser")
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n👋 Server stopped. Thanks for using AI Wedding Dress Studio!")
            sys.exit(0)

if __name__ == "__main__":
    main()