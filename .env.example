# -----------------------------------------------------------------------------
# 基础配置
# -----------------------------------------------------------------------------
NEXT_PUBLIC_WEB_URL=http://localhost:3000
NEXT_PUBLIC_PROJECT_NAME=NextSphere

# -----------------------------------------------------------------------------
# Auth配置 (必填)
# https://authjs.dev/getting-started/installation?framework=Next.js
# AUTH_SECRET可以通过运行 `openssl rand -base64 32` 生成
# -----------------------------------------------------------------------------
AUTH_SECRET=

# Google认证 (可选)
# https://authjs.dev/getting-started/providers/google
AUTH_GOOGLE_ID=
AUTH_GOOGLE_SECRET=
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED=true
NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED=false

# Github认证 (可选)
# https://authjs.dev/getting-started/providers/github
AUTH_GITHUB_ID=
AUTH_GITHUB_SECRET=
NEXT_PUBLIC_AUTH_GITHUB_ENABLED=true

# -----------------------------------------------------------------------------
# Stripe支付配置 (必填)
# https://docs.stripe.com/keys
# https://docs.stripe.com/checkout/embedded/quickstart
# -----------------------------------------------------------------------------
STRIPE_PUBLIC_KEY=
STRIPE_PRIVATE_KEY=
STRIPE_WEBHOOK_SECRET=

# -----------------------------------------------------------------------------
# 数据库配置 (必填)
# 支持 Neon 或 Supabase
# -----------------------------------------------------------------------------
# Neon数据库
# https://neon.tech/docs/guides/nextjs
DATABASE_URL=

# Supabase数据库 (可选)
# https://supabase.com/docs/guides/getting-started/quickstarts/nextjs
# SUPABASE_URL=
# SUPABASE_ANON_KEY=

# -----------------------------------------------------------------------------
# AI图片生成配置 (必填)
# -----------------------------------------------------------------------------
# 图片生成模式选择: openai, henapi 或 apicore
AI_GENERATION_MODE=openai

# OpenAI配置 (当AI_GENERATION_MODE=openai时使用)
OPENAI_API_KEY=
OPENAI_BASE_URL=https://api.laozhang.ai/v1

# HenAPI配置 (当AI_GENERATION_MODE=henapi时使用)
HENAPI_API_KEY=
HENAPI_BASE_URL=https://www.henapi.top/v1

# APICore配置 (当AI_GENERATION_MODE=apicore时使用)
APICORE_API_KEY=
APICORE_BASE_URL=https://api.apicore.ai/v1

# 开发模式配置
USE_PLACEHOLDER_IMAGES=false
DEBUG_MODE=false