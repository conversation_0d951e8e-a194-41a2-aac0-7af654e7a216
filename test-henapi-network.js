#!/usr/bin/env node

/**
 * Test HenAPI network connectivity
 * This script tests basic network connectivity to HenAPI endpoint
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');

console.log("🌐 Testing HenAPI Network Connectivity");
console.log("=" .repeat(50));

async function testNetworkConnectivity() {
  const testUrl = "https://www.henapi.top/v1/images/generations";
  
  console.log(`📍 Testing connectivity to: ${testUrl}`);
  
  try {
    const url = new URL(testUrl);
    console.log(`🔍 URL Analysis:`, {
      protocol: url.protocol,
      hostname: url.hostname,
      port: url.port || (url.protocol === 'https:' ? 443 : 80),
      pathname: url.pathname
    });
    
    // Test DNS resolution
    console.log("\n🔍 Testing DNS resolution...");
    const dns = require('dns').promises;
    try {
      const addresses = await dns.lookup(url.hostname);
      console.log(`✅ DNS resolution successful: ${url.hostname} -> ${addresses.address}`);
    } catch (dnsError) {
      console.log(`❌ DNS resolution failed: ${dnsError.message}`);
      return;
    }
    
    // Test basic HTTP connectivity
    console.log("\n🔍 Testing HTTP connectivity...");
    
    return new Promise((resolve, reject) => {
      const client = url.protocol === 'https:' ? https : http;
      const options = {
        hostname: url.hostname,
        port: url.port || (url.protocol === 'https:' ? 443 : 80),
        path: url.pathname,
        method: 'GET',
        timeout: 10000,
        headers: {
          'User-Agent': 'HenAPI-Test/1.0'
        }
      };
      
      const req = client.request(options, (res) => {
        console.log(`📥 Response Status: ${res.statusCode} ${res.statusMessage}`);
        console.log(`📥 Response Headers:`, res.headers);
        
        let data = '';
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          console.log(`📦 Response Body Length: ${data.length} bytes`);
          if (data.length < 500) {
            console.log(`📦 Response Body: ${data}`);
          } else {
            console.log(`📦 Response Body Preview: ${data.substring(0, 200)}...`);
          }
          
          if (res.statusCode === 200) {
            console.log("✅ Basic HTTP connectivity successful");
          } else if (res.statusCode === 405) {
            console.log("✅ Endpoint reachable (405 Method Not Allowed is expected for GET request)");
          } else if (res.statusCode === 401) {
            console.log("✅ Endpoint reachable (401 Unauthorized is expected without API key)");
          } else {
            console.log(`⚠️  Unexpected status code: ${res.statusCode}`);
          }
          resolve();
        });
      });
      
      req.on('error', (error) => {
        console.log(`❌ HTTP request failed: ${error.message}`);
        console.log(`❌ Error details:`, {
          code: error.code,
          errno: error.errno,
          syscall: error.syscall,
          address: error.address,
          port: error.port
        });
        
        if (error.code === 'ENOTFOUND') {
          console.log("💡 DNS resolution failed - check internet connection");
        } else if (error.code === 'ECONNREFUSED') {
          console.log("💡 Connection refused - service may be down");
        } else if (error.code === 'ETIMEDOUT') {
          console.log("💡 Connection timeout - network or firewall issue");
        } else if (error.code === 'CERT_HAS_EXPIRED') {
          console.log("💡 SSL certificate expired");
        } else if (error.code === 'UNABLE_TO_VERIFY_LEAF_SIGNATURE') {
          console.log("💡 SSL certificate verification failed");
        }
        
        reject(error);
      });
      
      req.on('timeout', () => {
        console.log("❌ Request timeout");
        req.destroy();
        reject(new Error('Request timeout'));
      });
      
      req.end();
    });
    
  } catch (error) {
    console.log(`❌ Network test failed: ${error.message}`);
  }
}

async function testWithCurl() {
  console.log("\n🔧 Alternative: Test with curl command");
  console.log("-" .repeat(30));
  
  const curlCommand = `curl -v -X GET "https://www.henapi.top/v1/images/generations" \\
  -H "User-Agent: HenAPI-Test/1.0" \\
  --connect-timeout 10 \\
  --max-time 30`;
  
  console.log("Run this command in your terminal:");
  console.log(curlCommand);
  
  console.log("\nExpected results:");
  console.log("- Connection successful (even if 401/405 error)");
  console.log("- SSL handshake successful");
  console.log("- Response received");
}

async function testWithActualAPICall() {
  console.log("\n🔧 Test with actual API call (requires API key)");
  console.log("-" .repeat(30));
  
  require('dotenv').config({ path: '.env.local' });
  
  const apiKey = process.env.HENAPI_API_KEY;
  if (!apiKey) {
    console.log("⚠️  HENAPI_API_KEY not configured - skipping API test");
    return;
  }
  
  const curlCommand = `curl -X POST "https://www.henapi.top/v1/images/generations" \\
  -H "Authorization: Bearer ${apiKey}" \\
  -H "Content-Type: application/json" \\
  -d '{
    "model": "dall-e-3",
    "prompt": "A simple test image",
    "n": 1,
    "size": "1024x1024"
  }'`;
  
  console.log("Run this command to test actual API call:");
  console.log(curlCommand);
}

// Run tests
(async () => {
  try {
    await testNetworkConnectivity();
    await testWithCurl();
    await testWithActualAPICall();
    
    console.log("\n" + "=" .repeat(50));
    console.log("🎯 Network Test Summary:");
    console.log("1. Check the output above for connectivity issues");
    console.log("2. If DNS/HTTP fails, check your internet connection");
    console.log("3. If successful, the issue might be with the API request format");
    console.log("4. Try the curl commands manually for more details");
    
  } catch (error) {
    console.log(`❌ Test suite failed: ${error.message}`);
  }
})();
