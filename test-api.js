// Simple test script to verify the API endpoint
const testData = {
  photoUrl: "data:image/jpeg;base64,test",
  styles: ["chinese-traditional"], // Test with one style to avoid multiple API calls
  userId: "test-user"
};

console.log("Testing API endpoint with data:", testData);
console.log("Note: This will test the prompt optimization for front-facing bride photos");

fetch('http://localhost:3000/api/generate', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(testData),
})
.then(response => response.json())
.then(data => {
  console.log("API Response:", data);
  if (data.photos && data.photos.length > 0) {
    console.log("Generated photo URL:", data.photos[0].imageUrl);
  }
})
.catch(error => {
  console.error("Error:", error);
});
