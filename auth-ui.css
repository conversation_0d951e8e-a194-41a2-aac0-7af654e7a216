/* Authentication UI Styles */

/* Modal Overlay */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease-out;
}

/* Auth Modal */
.auth-modal {
    background: white;
    border-radius: 20px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: slideUp 0.3s ease-out;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30px 30px 0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 30px;
}

.modal-header h2 {
    font-family: 'Playfair Display', serif;
    font-size: 1.8rem;
    color: #333;
    margin: 0;
}

.close-modal {
    font-size: 2rem;
    color: #999;
    cursor: pointer;
    transition: color 0.3s ease;
    line-height: 1;
}

.close-modal:hover {
    color: #667eea;
}

.modal-body {
    padding: 0 30px 30px;
}

/* Authentication Forms */
.auth-tabs {
    display: flex;
    margin-bottom: 30px;
    border-bottom: 1px solid #f0f0f0;
}

.auth-tab {
    flex: 1;
    padding: 15px;
    text-align: center;
    background: none;
    border: none;
    font-size: 1rem;
    font-weight: 600;
    color: #999;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.auth-tab.active {
    color: #667eea;
    border-bottom-color: #667eea;
}

.auth-form {
    display: none;
}

.auth-form.active {
    display: block;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #f0f0f0;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.auth-submit {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.auth-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.auth-submit:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Subscription Status - IMPROVED LAYOUT */
.subscription-status {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 0.85rem;
    min-width: 180px;
    text-align: center;
}

.status-trial,
.status-active,
.status-expired,
.status-no-plan {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.plan-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.plan-details {
    font-size: 0.75rem;
    opacity: 0.9;
}

.status-trial {
    color: #f39c12;
    font-weight: 600;
}

.status-active {
    color: #2ecc71;
    font-weight: 600;
}

.status-expired {
    color: #e74c3c;
    font-weight: 600;
}

.status-inactive {
    color: #999;
    font-weight: 600;
}

.photos-remaining {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-top: 5px;
}

/* User Menu */
.user-menu {
    position: relative;
    display: inline-block;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.user-avatar:hover {
    transform: scale(1.1);
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    min-width: 200px;
    padding: 10px 0;
    display: none;
    z-index: 1000;
}

.user-dropdown.show {
    display: block;
    animation: slideDown 0.3s ease-out;
}

.user-dropdown a {
    display: block;
    padding: 12px 20px;
    color: #333;
    text-decoration: none;
    transition: background 0.3s ease;
}

.user-dropdown a:hover {
    background: #f8f9fa;
}

/* Welcome Content */
.welcome-content {
    text-align: center;
}

.welcome-content h3 {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: #333;
}

.trial-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin: 30px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 15px;
}

.trial-stat {
    text-align: center;
}

.trial-stat .number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
    font-family: 'Playfair Display', serif;
}

.trial-stat .label {
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
}

/* Subscription Required */
.subscription-required {
    text-align: center;
}

.quick-plans {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin: 30px 0;
}

.quick-plan {
    padding: 20px;
    border: 2px solid #f0f0f0;
    border-radius: 15px;
    text-align: center;
    transition: all 0.3s ease;
}

.quick-plan.featured {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
}

.quick-plan h4 {
    font-family: 'Playfair Display', serif;
    margin-bottom: 10px;
    color: #333;
}

.quick-plan .price {
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 10px;
}

.quick-plan p {
    color: #666;
    margin-bottom: 15px;
}

/* Payment Modal */
.payment-modal {
    max-width: 600px;
}

.plan-summary {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 30px;
    text-align: center;
}

.plan-summary h3 {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #333;
}

.plan-summary .price {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 20px;
}

.plan-summary .features {
    list-style: none;
    text-align: left;
    max-width: 300px;
    margin: 0 auto;
}

.plan-summary .features li {
    padding: 5px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.plan-summary .features i {
    color: #2ecc71;
    width: 16px;
}

.payment-form h4 {
    margin-bottom: 20px;
    color: #333;
}

/* Subscription Selection Modal */
.subscription-selection {
    text-align: center;
}

.subscription-selection h3 {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: #333;
}

.plan-selection-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.plan-option {
    background: white;
    border: 2px solid #f0f0f0;
    border-radius: 15px;
    padding: 25px 20px;
    text-align: center;
    position: relative;
    transition: all 0.3s ease;
    cursor: pointer;
}

.plan-option:hover {
    border-color: #667eea;
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
}

.plan-option[data-plan="free_trial"] {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
}

.plan-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.plan-option h4 {
    font-family: 'Playfair Display', serif;
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: #333;
}

.plan-price {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 5px;
}

.plan-price span {
    font-size: 0.8rem;
    color: #666;
}

.plan-duration {
    color: #666;
    margin-bottom: 20px;
    font-size: 0.9rem;
}

.plan-features {
    list-style: none;
    margin-bottom: 25px;
    text-align: left;
}

.plan-features li {
    padding: 5px 0;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.plan-features i {
    color: #2ecc71;
    width: 16px;
}

.selection-note {
    font-size: 0.9rem;
    color: #666;
    margin-top: 20px;
    font-style: italic;
}

/* Subscription Success */
.subscription-success {
    text-align: center;
}

.subscription-success h3 {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: #333;
}

.subscription-details {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 15px;
    margin: 20px 0;
}

.subscription-details .detail {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.subscription-details .detail:last-child {
    border-bottom: none;
}

.subscription-details .label {
    font-weight: 600;
    color: #666;
}

.subscription-details .value {
    font-weight: 600;
    color: #333;
}

/* Navigation Auth Elements - IMPROVED LAYOUT */
.auth-element {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-element {
    display: none;
    align-items: center;
    gap: 15px;
}

/* Better organization for user elements */
.user-element .subscription-status {
    margin-right: 10px;
}

.user-element .user-menu {
    margin-left: 5px;
}

.auth-btn {
    padding: 8px 16px;
    border: 2px solid #667eea;
    border-radius: 20px;
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.auth-btn:hover {
    background: #667eea;
    color: white;
}

.auth-btn.primary {
    background: #667eea;
    color: white;
}

.auth-btn.primary:hover {
    background: #5a6fd8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-modal {
        width: 95%;
        margin: 20px;
    }
    
    .modal-header,
    .modal-body {
        padding: 20px;
    }
    
    .quick-plans {
        grid-template-columns: 1fr;
    }
    
    .trial-info {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .auth-element {
        flex-direction: column;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .modal-header h2 {
        font-size: 1.5rem;
    }
    
    .trial-stat .number {
        font-size: 1.5rem;
    }
    
    .plan-summary .price {
        font-size: 1.5rem;
    }
}

/* Animations */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading States */
.loading-auth {
    position: relative;
    pointer-events: none;
}

.loading-auth::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Subscription Badges */
.subscription-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.subscription-badge.trial {
    background: #fff3cd;
    color: #856404;
}

.subscription-badge.active {
    background: #d4edda;
    color: #155724;
}

.subscription-badge.expired {
    background: #f8d7da;
    color: #721c24;
}