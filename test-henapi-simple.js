#!/usr/bin/env node

/**
 * Simple HenAPI test using Node.js fetch
 */

require('dotenv').config({ path: '.env.local' });

console.log("🧪 Simple HenAPI Test");
console.log("=" .repeat(40));

async function testHenAPI() {
  const apiKey = process.env.HENAPI_API_KEY;
  const baseURL = process.env.HENAPI_BASE_URL || "https://www.henapi.top/v1";
  
  console.log("📋 Configuration:");
  console.log(`  API Key: ${apiKey ? apiKey.substring(0, 8) + '...' + apiKey.slice(-4) : 'NOT_SET'}`);
  console.log(`  Base URL: ${baseURL}`);
  
  if (!apiKey) {
    console.log("❌ HENAPI_API_KEY not configured");
    return;
  }
  
  // Check if fetch is available
  if (typeof fetch === 'undefined') {
    console.log("⚠️  fetch not available in this Node.js version");
    console.log("💡 Please test manually with curl or upgrade to Node.js 18+");
    return;
  }
  
  try {
    console.log("\n🚀 Testing HenAPI...");
    
    const requestBody = {
      "model": "dall-e-3",
      "prompt": "A simple red rose",
      "n": 1,
      "size": "1024x1024"
    };
    
    console.log("📤 Request:", {
      url: `${baseURL}/images/generations`,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey.substring(0, 8)}...`,
        'Content-Type': 'application/json'
      },
      body: requestBody
    });
    
    const response = await fetch(`${baseURL}/images/generations`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });
    
    console.log(`📥 Response Status: ${response.status} ${response.statusText}`);
    
    const responseText = await response.text();
    console.log(`📦 Response Length: ${responseText.length} bytes`);
    
    if (response.ok) {
      try {
        const result = JSON.parse(responseText);
        console.log("✅ Success! Response:", {
          created: result.created,
          dataLength: result.data?.length || 0,
          hasImageUrl: !!(result.data?.[0]?.url),
          imageUrlPreview: result.data?.[0]?.url ? result.data[0].url.substring(0, 60) + "..." : "N/A"
        });
      } catch (parseError) {
        console.log("⚠️  Response is not valid JSON:", responseText.substring(0, 200));
      }
    } else {
      console.log("❌ Request failed:", responseText);
      
      try {
        const errorResult = JSON.parse(responseText);
        console.log("📋 Error details:", errorResult);
      } catch (parseError) {
        console.log("📋 Raw error response:", responseText);
      }
    }
    
  } catch (error) {
    console.log("❌ Test failed:", error.message);
    
    if (error.name === 'AbortError') {
      console.log("💡 Request was aborted (timeout)");
    } else if (error.message.includes('fetch failed')) {
      console.log("💡 Network error - check internet connection");
    } else if (error.message.includes('ENOTFOUND')) {
      console.log("💡 DNS resolution failed");
    } else if (error.message.includes('ECONNREFUSED')) {
      console.log("💡 Connection refused");
    }
  }
}

// Check Node.js version
const nodeVersion = process.version;
console.log(`📋 Node.js Version: ${nodeVersion}`);

const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
if (majorVersion < 18) {
  console.log("⚠️  Node.js 18+ recommended for built-in fetch support");
}

testHenAPI().then(() => {
  console.log("\n" + "=" .repeat(40));
  console.log("🎯 Test completed");
});
