#!/usr/bin/env node

/**
 * Test the fetch endpoint to diagnose Hen<PERSON><PERSON> issues
 */

console.log("🧪 Testing Fetch Endpoint");
console.log("=" .repeat(40));

async function testFetchEndpoint() {
  const baseUrl = "http://localhost:3000";
  
  console.log("📋 Testing environment and connectivity...");
  
  try {
    // Test basic fetch availability
    const response = await fetch(`${baseUrl}/api/test-fetch`);
    
    if (!response.ok) {
      console.log(`❌ Test endpoint failed: ${response.status} ${response.statusText}`);
      return;
    }
    
    const result = await response.json();
    
    console.log("📊 Environment Test Results:");
    console.log(`  Node.js Version: ${result.environment.nodeVersion}`);
    console.log(`  Has fetch: ${result.environment.hasFetch}`);
    console.log(`  Has global fetch: ${result.environment.hasGlobalFetch}`);
    console.log(`  Next.js Version: ${result.environment.nextjsVersion}`);
    
    if (result.error) {
      console.log(`❌ Environment Error: ${result.error}`);
      console.log(`💡 Suggestion: ${result.suggestion}`);
      return;
    }
    
    console.log("\n📊 HenAPI Connectivity Test:");
    if (result.henApiTest.success) {
      console.log("✅ HenAPI endpoint is reachable");
      console.log(`  Status: ${result.henApiTest.status} ${result.henApiTest.statusText}`);
      console.log(`  URL: ${result.henApiTest.url}`);
    } else {
      console.log("❌ HenAPI connectivity failed");
      console.log(`  Error: ${result.henApiTest.error}`);
      console.log(`  Error Type: ${result.henApiTest.errorType}`);
    }
    
    // Test actual API call if connectivity is OK
    if (result.henApiTest.success) {
      console.log("\n🚀 Testing actual API call...");
      
      const apiResponse = await fetch(`${baseUrl}/api/test-fetch`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ testApiCall: true })
      });
      
      const apiResult = await apiResponse.json();
      
      if (apiResult.success) {
        console.log("✅ Actual API call successful!");
        console.log(`  Status: ${apiResult.status} ${apiResult.statusText}`);
        console.log(`  Response Length: ${apiResult.responseLength} bytes`);
        
        if (apiResult.response.data) {
          console.log(`  Generated Images: ${apiResult.response.data.length}`);
          console.log(`  Image URL: ${apiResult.response.data[0]?.url ? 'Present' : 'Missing'}`);
        }
      } else {
        console.log("❌ Actual API call failed");
        console.log(`  Status: ${apiResult.status || 'N/A'}`);
        console.log(`  Error: ${apiResult.error}`);
        console.log(`  Error Type: ${apiResult.errorType}`);
      }
    }
    
  } catch (error) {
    console.log(`❌ Test failed: ${error.message}`);
    
    if (error.message.includes('fetch is not defined')) {
      console.log("💡 This script requires Node.js 18+ or a fetch polyfill");
      console.log("💡 The actual Next.js application should work correctly");
    } else if (error.message.includes('ECONNREFUSED')) {
      console.log("💡 Make sure the development server is running: pnpm dev");
    }
  }
}

// Check if fetch is available
if (typeof fetch === 'undefined') {
  console.log("⚠️  fetch not available in this Node.js version");
  console.log("💡 Please start the development server and test manually:");
  console.log("   1. Start server: pnpm dev");
  console.log("   2. Visit: http://localhost:3000/api/test-fetch");
  console.log("   3. Check browser console for detailed logs");
} else {
  testFetchEndpoint().then(() => {
    console.log("\n" + "=" .repeat(40));
    console.log("🎯 Test completed");
  });
}
