// Test script to verify the results page fix
const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

async function testResultsPage() {
  console.log("🧪 Testing results page SSR fix...");

  try {
    const { stdout, stderr } = await execAsync('curl -s -w "%{http_code}" http://localhost:3000/en/results');

    // Extract status code (last 3 characters)
    const statusCode = stdout.slice(-3);
    const html = stdout.slice(0, -3);

    console.log("📥 Response status:", statusCode);

    if (statusCode === '200') {
      // Check if the page rendered without errors
      if (html.includes('window is not defined')) {
        console.log("❌ Still has window.location error");
        return false;
      }

      if (html.includes('No photos generated yet')) {
        console.log("✅ Results page renders correctly");
        console.log("✅ Shows 'No photos generated yet' message");
        return true;
      }

      if (html.includes('<!DOCTYPE html>')) {
        console.log("✅ Results page renders HTML correctly");
        return true;
      }

      console.log("⚠️ Page rendered but content unclear");
      return true;

    } else {
      console.log("❌ Response status:", statusCode);
      return false;
    }

  } catch (error) {
    console.error("❌ Error testing results page:", error.message);
    return false;
  }
}

async function testBasicAccess() {
  console.log("\n🧪 Testing basic page access...");

  try {
    const { stdout } = await execAsync('curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/en/results');

    if (stdout.trim() === '200') {
      console.log("✅ Results page is accessible");
      return true;
    } else {
      console.log("❌ Results page returned status:", stdout.trim());
      return false;
    }

  } catch (error) {
    console.error("❌ Error testing basic access:", error.message);
    return false;
  }
}

async function runTests() {
  console.log("🔍 Testing Results Page SSR Fix\n");

  const test1 = await testResultsPage();
  const test2 = await testBasicAccess();

  console.log("\n📋 Test Summary:");
  console.log(`  SSR content test: ${test1 ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`  Basic access test: ${test2 ? '✅ PASS' : '❌ FAIL'}`);

  if (test1 && test2) {
    console.log("\n🎉 All tests passed! Results page SSR fix is working correctly.");
    console.log("✅ No more 'window is not defined' errors");
    console.log("✅ Page renders properly on server side");
  } else {
    console.log("\n❌ Some tests failed. Please check the implementation.");
  }
}

runTests();
