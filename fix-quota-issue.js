#!/usr/bin/env node

// 配额问题快速修复脚本
// 自动诊断和修复HenAPI配额不足问题

const fs = require('fs');
const path = require('path');

console.log('🔧 HenAPI配额问题快速修复工具');
console.log('=====================================\n');

// 检查环境变量文件
function loadEnvFile() {
  const envFiles = ['.env.local', '.env'];
  let envVars = {};
  
  for (const envFile of envFiles) {
    if (fs.existsSync(envFile)) {
      console.log(`📁 读取环境变量文件: ${envFile}`);
      const envContent = fs.readFileSync(envFile, 'utf8');
      const envLines = envContent.split('\n');
      
      envLines.forEach(line => {
        const trimmed = line.trim();
        if (trimmed && !trimmed.startsWith('#')) {
          const [key, ...valueParts] = trimmed.split('=');
          if (key && valueParts.length > 0) {
            envVars[key.trim()] = valueParts.join('=').trim();
          }
        }
      });
      break;
    }
  }
  
  return envVars;
}

// 检查当前配置
function checkCurrentConfig(envVars) {
  console.log('🔍 检查当前配置:');
  
  const mode = envVars.AI_GENERATION_MODE || 'openai';
  const hasHenAPI = !!envVars.HENAPI_API_KEY;
  const hasOpenAI = !!envVars.OPENAI_API_KEY;
  const hasAPICore = !!envVars.APICORE_API_KEY;
  const debugMode = envVars.DEBUG_MODE === 'true';
  const usePlaceholder = envVars.USE_PLACEHOLDER_IMAGES === 'true';
  
  console.log(`   当前模式: ${mode}`);
  console.log(`   HenAPI密钥: ${hasHenAPI ? '✅ 已配置' : '❌ 未配置'}`);
  console.log(`   OpenAI密钥: ${hasOpenAI ? '✅ 已配置' : '❌ 未配置'}`);
  console.log(`   APICore密钥: ${hasAPICore ? '✅ 已配置' : '❌ 未配置'}`);
  console.log(`   调试模式: ${debugMode ? '✅ 已启用' : '❌ 未启用'}`);
  console.log(`   占位图片: ${usePlaceholder ? '✅ 已启用' : '❌ 未启用'}\n`);
  
  return {
    mode,
    hasHenAPI,
    hasOpenAI,
    hasAPICore,
    debugMode,
    usePlaceholder
  };
}

// 提供修复建议
function provideSolutions(config) {
  console.log('💡 修复建议:');
  
  const solutions = [];
  
  if (config.mode === 'henapi' && !config.hasHenAPI) {
    solutions.push({
      priority: 'high',
      title: '配置HenAPI密钥',
      description: '当前模式为henapi但未配置API密钥',
      action: '在.env.local中添加: HENAPI_API_KEY=your_key_here'
    });
  }
  
  if (config.mode === 'henapi') {
    solutions.push({
      priority: 'high',
      title: '切换到其他AI模式',
      description: '避免HenAPI配额限制',
      action: '在.env.local中设置: AI_GENERATION_MODE=openai 或 AI_GENERATION_MODE=apicore'
    });
  }
  
  if (!config.debugMode) {
    solutions.push({
      priority: 'medium',
      title: '启用调试模式',
      description: '获取更详细的错误信息',
      action: '在.env.local中添加: DEBUG_MODE=true'
    });
  }
  
  if (!config.usePlaceholder) {
    solutions.push({
      priority: 'medium',
      title: '启用占位图片',
      description: '在开发环境中避免API调用',
      action: '在.env.local中添加: USE_PLACEHOLDER_IMAGES=true'
    });
  }
  
  if (config.hasOpenAI) {
    solutions.push({
      priority: 'low',
      title: '使用OpenAI模式',
      description: '已配置OpenAI密钥，可以切换使用',
      action: '在.env.local中设置: AI_GENERATION_MODE=openai'
    });
  }
  
  if (config.hasAPICore) {
    solutions.push({
      priority: 'low',
      title: '使用APICore模式',
      description: '已配置APICore密钥，可以切换使用',
      action: '在.env.local中设置: AI_GENERATION_MODE=apicore'
    });
  }
  
  // 按优先级排序
  const priorityOrder = { high: 3, medium: 2, low: 1 };
  solutions.sort((a, b) => priorityOrder[b.priority] - priorityOrder[a.priority]);
  
  solutions.forEach((solution, index) => {
    const priorityIcon = solution.priority === 'high' ? '🔴' : 
                        solution.priority === 'medium' ? '🟡' : '🟢';
    console.log(`\n   ${priorityIcon} ${index + 1}. ${solution.title}`);
    console.log(`      ${solution.description}`);
    console.log(`      操作: ${solution.action}`);
  });
  
  return solutions;
}

// 生成修复后的环境变量文件
function generateFixedEnvFile(envVars, solutions) {
  console.log('\n🔧 生成修复建议的环境变量配置:');
  
  const fixedEnv = { ...envVars };
  
  // 应用高优先级修复
  const highPrioritySolutions = solutions.filter(s => s.priority === 'high');
  
  if (highPrioritySolutions.some(s => s.title.includes('切换到其他AI模式'))) {
    if (envVars.OPENAI_API_KEY) {
      fixedEnv.AI_GENERATION_MODE = 'openai';
      console.log('   ✅ 切换到OpenAI模式');
    } else if (envVars.APICORE_API_KEY) {
      fixedEnv.AI_GENERATION_MODE = 'apicore';
      console.log('   ✅ 切换到APICore模式');
    } else {
      fixedEnv.USE_PLACEHOLDER_IMAGES = 'true';
      console.log('   ✅ 启用占位图片模式');
    }
  }
  
  // 启用调试模式
  if (!envVars.DEBUG_MODE) {
    fixedEnv.DEBUG_MODE = 'true';
    console.log('   ✅ 启用调试模式');
  }
  
  // 生成新的环境变量文件内容
  const envContent = Object.entries(fixedEnv)
    .map(([key, value]) => `${key}=${value}`)
    .join('\n');
  
  console.log('\n📝 建议的.env.local内容:');
  console.log('-----------------------------------');
  console.log(envContent);
  console.log('-----------------------------------\n');
  
  return envContent;
}

// 提供API测试命令
function provideTestCommands() {
  console.log('🧪 测试命令:');
  console.log('\n1. 检查API配置:');
  console.log('   curl http://localhost:3000/api/generate');
  
  console.log('\n2. 检查配额状态:');
  console.log('   curl http://localhost:3000/api/debug-quota');
  
  console.log('\n3. 重置配额（开发环境）:');
  console.log('   curl -X POST http://localhost:3000/api/debug-quota \\');
  console.log('     -H "Content-Type: application/json" \\');
  console.log('     -d \'{"action": "reset", "quota": 10000}\'');
  
  console.log('\n4. 访问调试面板:');
  console.log('   http://localhost:3000/debug-quota');
  
  console.log('\n5. 测试图片编辑:');
  console.log('   http://localhost:3000/test-edit\n');
}

// 主函数
async function main() {
  try {
    // 加载环境变量
    const envVars = loadEnvFile();
    
    if (Object.keys(envVars).length === 0) {
      console.log('❌ 未找到环境变量文件');
      console.log('💡 请复制 .env.example 到 .env.local 并配置相关变量\n');
      return;
    }
    
    // 检查当前配置
    const config = checkCurrentConfig(envVars);
    
    // 提供修复建议
    const solutions = provideSolutions(config);
    
    // 生成修复后的配置
    if (solutions.length > 0) {
      generateFixedEnvFile(envVars, solutions);
    } else {
      console.log('\n✅ 配置看起来正常，如果仍有问题，请检查API密钥是否有效\n');
    }
    
    // 提供测试命令
    provideTestCommands();
    
    console.log('📚 相关文档:');
    console.log('   - 配额问题排查: QUOTA_TROUBLESHOOTING_GUIDE.md');
    console.log('   - HenAPI集成: HENAPI_INTEGRATION_GUIDE.md');
    console.log('   - APICore集成: APICORE_INTEGRATION_GUIDE.md\n');
    
    console.log('🎯 快速修复步骤:');
    console.log('1. 根据上述建议修改 .env.local 文件');
    console.log('2. 重启开发服务器: pnpm dev');
    console.log('3. 访问调试面板检查状态');
    console.log('4. 测试图片生成功能\n');
    
  } catch (error) {
    console.error('❌ 修复过程中出现错误:', error.message);
  }
}

// 运行主函数
main();
