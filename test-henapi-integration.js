#!/usr/bin/env node

/**
 * Test script for HenAPI integration
 * Tests the new dual-mode image generation API
 */

const fs = require('fs');
const path = require('path');

console.log("🧪 Testing HenAPI Integration");
console.log("=" .repeat(50));

// Check 1: Verify environment configuration
console.log("\n1. Checking environment configuration...");
const hasEnvExample = fs.existsSync('.env.example');
console.log(`   ✅ .env.example: ${hasEnvExample ? 'EXISTS' : 'MISSING'}`);

if (hasEnvExample) {
  const envContent = fs.readFileSync('.env.example', 'utf8');
  const hasAIMode = envContent.includes('AI_GENERATION_MODE');
  const hasHenAPIKey = envContent.includes('HENAPI_API_KEY');
  const hasHenAPIURL = envContent.includes('HENAPI_BASE_URL');
  
  console.log(`   ✅ AI_GENERATION_MODE config: ${hasAIMode ? 'PRESENT' : 'MISSING'}`);
  console.log(`   ✅ HENAPI_API_KEY config: ${hasHenAPIKey ? 'PRESENT' : 'MISSING'}`);
  console.log(`   ✅ HENAPI_BASE_URL config: ${hasHenAPIURL ? 'PRESENT' : 'MISSING'}`);
}

// Check 2: Verify HenAPI service file
console.log("\n2. Checking HenAPI service file...");
const henApiServicePath = 'src/lib/henapi.ts';
const hasHenApiService = fs.existsSync(henApiServicePath);
console.log(`   ✅ HenAPI service: ${hasHenApiService ? 'EXISTS' : 'MISSING'}`);

if (hasHenApiService) {
  const serviceContent = fs.readFileSync(henApiServicePath, 'utf8');
  const hasHenAPIClass = serviceContent.includes('class HenAPIService');
  const hasGenerateMethod = serviceContent.includes('generateImage');
  const hasFactory = serviceContent.includes('createHenAPIService');
  
  console.log(`   ✅ HenAPIService class: ${hasHenAPIClass ? 'PRESENT' : 'MISSING'}`);
  console.log(`   ✅ generateImage method: ${hasGenerateMethod ? 'PRESENT' : 'MISSING'}`);
  console.log(`   ✅ Factory function: ${hasFactory ? 'PRESENT' : 'MISSING'}`);
}

// Check 3: Verify API route modifications
console.log("\n3. Checking API route modifications...");
const apiRoutePath = 'src/app/api/generate/route.ts';
const hasApiRoute = fs.existsSync(apiRoutePath);
console.log(`   ✅ API route file: ${hasApiRoute ? 'EXISTS' : 'MISSING'}`);

if (hasApiRoute) {
  const routeContent = fs.readFileSync(apiRoutePath, 'utf8');
  const hasHenApiImport = routeContent.includes('createHenAPIService');
  const hasModeConfig = routeContent.includes('AI_GENERATION_MODE');
  const hasHenApiLogic = routeContent.includes('henApiService');
  const hasModeSwitch = routeContent.includes('henapi') && routeContent.includes('openai');
  
  console.log(`   ✅ HenAPI import: ${hasHenApiImport ? 'PRESENT' : 'MISSING'}`);
  console.log(`   ✅ Mode configuration: ${hasModeConfig ? 'PRESENT' : 'MISSING'}`);
  console.log(`   ✅ HenAPI service logic: ${hasHenApiLogic ? 'PRESENT' : 'MISSING'}`);
  console.log(`   ✅ Mode switching logic: ${hasModeSwitch ? 'PRESENT' : 'MISSING'}`);
}

// Check 4: Test API endpoint configuration
console.log("\n4. Testing API endpoint configuration...");

async function testApiEndpoint() {
  try {
    const response = await fetch('http://localhost:3000/api/generate', {
      method: 'GET'
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log(`   ✅ API endpoint: ACCESSIBLE`);
      console.log(`   ✅ Current mode: ${data.currentMode || 'NOT_CONFIGURED'}`);
      console.log(`   ✅ Supported modes: ${data.supportedModes ? data.supportedModes.join(', ') : 'NOT_CONFIGURED'}`);
      
      if (data.configuration) {
        console.log(`   ✅ Has OpenAI key: ${data.configuration.hasOpenAIKey ? 'YES' : 'NO'}`);
        console.log(`   ✅ Has HenAPI key: ${data.configuration.hasHenAPIKey ? 'YES' : 'NO'}`);
      }
    } else {
      console.log(`   ❌ API endpoint: NOT_ACCESSIBLE (${response.status})`);
    }
  } catch (error) {
    console.log(`   ⚠️  API endpoint: NOT_RUNNING (${error.message})`);
    console.log(`   💡 Start the development server with: pnpm dev`);
  }
}

// Only test endpoint if we're not in CI environment
if (!process.env.CI) {
  testApiEndpoint();
}

console.log("\n" + "=" .repeat(50));
console.log("🎉 HenAPI Integration Test Complete!");
console.log("\n📋 Next Steps:");
console.log("1. Copy .env.example to .env.local");
console.log("2. Set AI_GENERATION_MODE=henapi (or openai)");
console.log("3. Add your HENAPI_API_KEY");
console.log("4. Start the development server: pnpm dev");
console.log("5. Test image generation with your preferred mode");

console.log("\n🔧 Configuration Examples:");
console.log("For HenAPI mode:");
console.log("  AI_GENERATION_MODE=henapi");
console.log("  HENAPI_API_KEY=your_henapi_key_here");
console.log("  HENAPI_BASE_URL=https://api.hdgsb.com/v1");

console.log("\nFor OpenAI mode:");
console.log("  AI_GENERATION_MODE=openai");
console.log("  OPENAI_API_KEY=your_openai_key_here");
console.log("  OPENAI_BASE_URL=https://api.laozhang.ai/v1");
