#!/usr/bin/env node

// APICore配置验证脚本
// 检查APICore集成是否正确配置

const fs = require('fs');
const path = require('path');

console.log('🔍 APICore配置验证');
console.log('=====================================\n');

// 检查环境变量文件
const envFiles = ['.env.local', '.env'];
let envFound = false;

for (const envFile of envFiles) {
  if (fs.existsSync(envFile)) {
    console.log(`✅ 找到环境变量文件: ${envFile}`);
    envFound = true;
    
    // 读取环境变量
    const envContent = fs.readFileSync(envFile, 'utf8');
    const envLines = envContent.split('\n');
    
    // 解析环境变量
    const envVars = {};
    envLines.forEach(line => {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=');
        if (key && valueParts.length > 0) {
          envVars[key.trim()] = valueParts.join('=').trim();
        }
      }
    });
    
    // 检查APICore相关配置
    console.log('\n📋 APICore配置检查:');
    console.log(`   AI_GENERATION_MODE: ${envVars.AI_GENERATION_MODE || '未设置'}`);
    console.log(`   APICORE_API_KEY: ${envVars.APICORE_API_KEY ? '已设置' : '未设置'}`);
    console.log(`   APICORE_BASE_URL: ${envVars.APICORE_BASE_URL || '未设置'}`);
    
    // 检查其他模式配置
    console.log('\n📋 其他模式配置:');
    console.log(`   OPENAI_API_KEY: ${envVars.OPENAI_API_KEY ? '已设置' : '未设置'}`);
    console.log(`   HENAPI_API_KEY: ${envVars.HENAPI_API_KEY ? '已设置' : '未设置'}`);
    
    // 设置环境变量到process.env
    Object.keys(envVars).forEach(key => {
      if (!process.env[key]) {
        process.env[key] = envVars[key];
      }
    });
    
    break;
  }
}

if (!envFound) {
  console.log('❌ 未找到环境变量文件 (.env.local 或 .env)');
  console.log('   请复制 .env.example 到 .env.local 并配置相关变量');
}

// 检查必要的文件
console.log('\n📁 文件结构检查:');

const requiredFiles = [
  'src/lib/apicore.ts',
  'src/app/api/edit-image/route.ts',
  'src/app/test-edit/page.tsx',
  'APICORE_INTEGRATION_GUIDE.md'
];

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`   ✅ ${file}: 存在`);
  } else {
    console.log(`   ❌ ${file}: 缺失`);
  }
});

// 检查APICore服务文件内容
const apicoreServicePath = 'src/lib/apicore.ts';
if (fs.existsSync(apicoreServicePath)) {
  const serviceContent = fs.readFileSync(apicoreServicePath, 'utf8');
  const hasAPICoreClass = serviceContent.includes('class APICoreService');
  const hasEditMethod = serviceContent.includes('editImage');
  const hasFactory = serviceContent.includes('createAPICoreService');
  
  console.log('\n🔧 APICore服务检查:');
  console.log(`   ✅ APICoreService类: ${hasAPICoreClass ? '存在' : '缺失'}`);
  console.log(`   ✅ editImage方法: ${hasEditMethod ? '存在' : '缺失'}`);
  console.log(`   ✅ 工厂函数: ${hasFactory ? '存在' : '缺失'}`);
}

// 检查API路由文件
const apiRoutePath = 'src/app/api/edit-image/route.ts';
if (fs.existsSync(apiRoutePath)) {
  const routeContent = fs.readFileSync(apiRoutePath, 'utf8');
  const hasAPICoreImport = routeContent.includes('createAPICoreService');
  const hasPOSTMethod = routeContent.includes('export async function POST');
  const hasGETMethod = routeContent.includes('export async function GET');
  const hasFormDataHandling = routeContent.includes('formData');
  
  console.log('\n🛣️ API路由检查:');
  console.log(`   ✅ APICore导入: ${hasAPICoreImport ? '存在' : '缺失'}`);
  console.log(`   ✅ POST方法: ${hasPOSTMethod ? '存在' : '缺失'}`);
  console.log(`   ✅ GET方法: ${hasGETMethod ? '存在' : '缺失'}`);
  console.log(`   ✅ FormData处理: ${hasFormDataHandling ? '存在' : '缺失'}`);
}

// 检查生成API是否已更新
const generateRoutePath = 'src/app/api/generate/route.ts';
if (fs.existsSync(generateRoutePath)) {
  const generateContent = fs.readFileSync(generateRoutePath, 'utf8');
  const hasAPICoreImport = generateContent.includes('createAPICoreService');
  const hasAPICoreMode = generateContent.includes('apicore');
  const hasAPICoreConfig = generateContent.includes('hasAPICoreKey');
  
  console.log('\n🔄 生成API更新检查:');
  console.log(`   ✅ APICore导入: ${hasAPICoreImport ? '存在' : '缺失'}`);
  console.log(`   ✅ APICore模式: ${hasAPICoreMode ? '支持' : '不支持'}`);
  console.log(`   ✅ APICore配置: ${hasAPICoreConfig ? '存在' : '缺失'}`);
}

// 测试API端点连接
console.log('\n🌐 API端点测试:');

async function testApiEndpoint() {
  try {
    const response = await fetch('http://localhost:3000/api/edit-image', {
      method: 'GET'
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log(`   ✅ 图片编辑API: 可访问`);
      console.log(`   ✅ 端点: ${data.endpoint || '未知'}`);
      console.log(`   ✅ 方法: ${data.method || '未知'}`);
      console.log(`   ✅ 内容类型: ${data.contentType || '未知'}`);
      
      if (data.configuration) {
        console.log(`   ✅ 有APICore密钥: ${data.configuration.hasAPICoreKey ? '是' : '否'}`);
        console.log(`   ✅ APICore URL: ${data.configuration.apicoreBaseURL || '未设置'}`);
      }
    } else {
      console.log(`   ❌ 图片编辑API: 不可访问 (${response.status})`);
    }
  } catch (error) {
    console.log(`   ❌ 图片编辑API: 连接失败 (${error.message})`);
    console.log('   💡 请确保开发服务器正在运行: pnpm dev');
  }
  
  try {
    const response = await fetch('http://localhost:3000/api/generate', {
      method: 'GET'
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log(`   ✅ 生成API: 可访问`);
      console.log(`   ✅ 当前模式: ${data.currentMode || '未知'}`);
      console.log(`   ✅ 支持模式: ${data.supportedModes ? data.supportedModes.join(', ') : '未知'}`);
      
      if (data.configuration) {
        console.log(`   ✅ 有APICore密钥: ${data.configuration.hasAPICoreKey ? '是' : '否'}`);
      }
    } else {
      console.log(`   ❌ 生成API: 不可访问 (${response.status})`);
    }
  } catch (error) {
    console.log(`   ❌ 生成API: 连接失败 (${error.message})`);
  }
}

// 运行测试
testApiEndpoint().then(() => {
  console.log('\n📊 配置总结:');
  
  const mode = process.env.AI_GENERATION_MODE;
  const hasAPICoreKey = !!process.env.APICORE_API_KEY;
  const apicoreUrl = process.env.APICORE_BASE_URL || 'https://api.apicore.ai/v1';
  
  if (mode === 'apicore' && hasAPICoreKey) {
    console.log('✅ APICore配置完整！');
    console.log('1. 重启开发服务器: pnpm dev');
    console.log('2. 测试图片编辑: http://localhost:3000/test-edit');
    console.log('3. 查看API文档: APICORE_INTEGRATION_GUIDE.md');
  } else {
    console.log('❌ APICore配置需要完善:');
    
    if (mode !== 'apicore') {
      console.log('- 设置 AI_GENERATION_MODE=apicore 在 .env.local 中');
    }
    
    if (!hasAPICoreKey) {
      console.log('- 添加 APICORE_API_KEY=your_api_key 在 .env.local 中');
    }
    
    console.log('- 参考 APICORE_INTEGRATION_GUIDE.md 获取详细配置说明');
  }
}).catch(error => {
  console.error('测试过程中出现错误:', error);
});
