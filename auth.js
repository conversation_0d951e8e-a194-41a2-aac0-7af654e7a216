// Authentication and Subscription Management System
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.subscriptionPlans = {
            free_trial: {
                name: 'Free Trial',
                duration: 3, // days
                photos: 3,
                price: 0,
                features: ['3 AI-generated photos', 'All dress styles', 'All scene options', 'Standard quality']
            },
            pro: {
                name: 'Pro Package',
                duration: 30, // days
                photos: 20,
                price: 19,
                features: ['20 AI-generated photos', 'All dress styles', 'All scene options', 'Ultra-high quality', 'High-resolution downloads', 'Commercial use license']
            },
            wedding: {
                name: 'Wedding Package',
                duration: 30, // days
                photos: -1, // unlimited
                price: 49,
                features: ['Unlimited photos', 'All dress styles', 'All scene options', 'Ultra-high quality', 'High-resolution downloads', 'Commercial use license', 'Priority support', 'Custom requests']
            }
        };
        this.init();
    }

    init() {
        this.loadUserData();
        this.setupEventListeners();
        this.updateUI();
    }

    // Load user data from localStorage
    loadUserData() {
        const userData = localStorage.getItem('weddingAI_user');
        if (userData) {
            this.currentUser = JSON.parse(userData);
            this.checkSubscriptionStatus();
        }
    }

    // Save user data to localStorage
    saveUserData() {
        localStorage.setItem('weddingAI_user', JSON.stringify(this.currentUser));
    }

    // Check if user is authenticated
    isAuthenticated() {
        return this.currentUser !== null;
    }

    // Check if user has active subscription
    hasActiveSubscription() {
        if (!this.currentUser || !this.currentUser.subscriptionType) return false;
        
        const now = new Date();
        const subscriptionEnd = new Date(this.currentUser.subscriptionEnd);
        return now <= subscriptionEnd;
    }

    // Check if user is in trial period
    isInTrialPeriod() {
        if (!this.currentUser) return false;
        return this.currentUser.subscriptionType === 'free_trial' && this.hasActiveSubscription();
    }

    // Get remaining trial days
    getRemainingTrialDays() {
        if (!this.isInTrialPeriod()) return 0;
        
        const now = new Date();
        const trialEnd = new Date(this.currentUser.subscriptionEnd);
        const diffTime = trialEnd - now;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return Math.max(0, diffDays);
    }

    // Get remaining photos for current subscription
    getRemainingPhotos() {
        if (!this.currentUser || !this.currentUser.subscriptionType) return 0;
        
        const plan = this.subscriptionPlans[this.currentUser.subscriptionType];
        if (!plan) return 0;
        if (plan.photos === -1) return -1; // unlimited
        
        return Math.max(0, plan.photos - this.currentUser.photosUsed);
    }

    // Check subscription status and update if expired
    checkSubscriptionStatus() {
        if (!this.currentUser) return;
        
        if (this.currentUser.subscriptionType && !this.hasActiveSubscription()) {
            // Subscription expired
            this.currentUser.subscriptionType = null;
            this.currentUser.subscriptionEnd = null;
            this.saveUserData();
            this.showSubscriptionExpiredModal();
        }
    }

    // Register new user with free trial
    async register(email, password, name) {
        try {
            // Simulate API call
            await this.simulateAPICall();
            
            const now = new Date();
            const trialEnd = new Date(now.getTime() + (3 * 24 * 60 * 60 * 1000)); // 3 days from now
            
            this.currentUser = {
                id: this.generateUserId(),
                email: email,
                name: name,
                subscriptionType: 'free_trial',
                subscriptionStart: now.toISOString(),
                subscriptionEnd: trialEnd.toISOString(),
                photosUsed: 0,
                createdAt: now.toISOString(),
                lastLogin: now.toISOString()
            };
            
            this.saveUserData();
            this.updateUI();
            this.showWelcomeModal();
            
            // Track registration event
            this.trackEvent('user_registered', {
                subscription_type: 'free_trial',
                trial_days: 3
            });
            
            return { success: true, user: this.currentUser };
        } catch (error) {
            console.error('Registration error:', error);
            return { success: false, error: error.message };
        }
    }

    // Login existing user - FIXED LOGIC
    async login(email, password) {
        try {
            // Simulate API call
            await this.simulateAPICall();
            
            // For demo purposes, create a user without subscription if none exists
            const userData = localStorage.getItem('weddingAI_user');
            if (userData) {
                this.currentUser = JSON.parse(userData);
                this.currentUser.lastLogin = new Date().toISOString();
                this.saveUserData();
                this.checkSubscriptionStatus();
                this.updateUI();
                
                this.trackEvent('user_login', {
                    subscription_type: this.currentUser.subscriptionType
                });
                
                return { success: true, user: this.currentUser };
            } else {
                // Create user without subscription - they need to choose a plan
                const now = new Date();
                this.currentUser = {
                    id: this.generateUserId(),
                    email: email,
                    name: email.split('@')[0], // Use email prefix as name
                    subscriptionType: null,
                    subscriptionStart: null,
                    subscriptionEnd: null,
                    photosUsed: 0,
                    createdAt: now.toISOString(),
                    lastLogin: now.toISOString()
                };
                
                this.saveUserData();
                this.updateUI();
                
                // Show subscription selection modal for new login
                setTimeout(() => {
                    this.showSubscriptionSelectionModal();
                }, 500);
                
                this.trackEvent('user_login', {
                    subscription_type: 'none',
                    new_user: true
                });
                
                return { success: true, user: this.currentUser };
            }
        } catch (error) {
            console.error('Login error:', error);
            return { success: false, error: error.message };
        }
    }

    // Logout user
    logout() {
        this.currentUser = null;
        localStorage.removeItem('weddingAI_user');
        this.updateUI();
        
        this.trackEvent('user_logout');
    }

    // Subscribe to a plan
    async subscribe(planType) {
        if (!this.currentUser) {
            throw new Error('User must be logged in to subscribe');
        }
        
        try {
            const plan = this.subscriptionPlans[planType];
            if (!plan) {
                throw new Error('Invalid subscription plan');
            }
            
            // Simulate payment processing
            await this.simulatePaymentProcessing(plan.price);
            
            const now = new Date();
            const subscriptionEnd = new Date(now.getTime() + (plan.duration * 24 * 60 * 60 * 1000));
            
            this.currentUser.subscriptionType = planType;
            this.currentUser.subscriptionStart = now.toISOString();
            this.currentUser.subscriptionEnd = subscriptionEnd.toISOString();
            this.currentUser.photosUsed = 0; // Reset photo count for new subscription
            
            this.saveUserData();
            this.updateUI();
            this.showSubscriptionSuccessModal(plan);
            
            this.trackEvent('subscription_purchased', {
                plan_type: planType,
                price: plan.price,
                duration: plan.duration
            });
            
            return { success: true, subscription: plan };
        } catch (error) {
            console.error('Subscription error:', error);
            return { success: false, error: error.message };
        }
    }

    // Use a photo (decrement count)
    usePhoto() {
        if (!this.canUseService()) {
            throw new Error('No active subscription or photos remaining');
        }
        
        const remaining = this.getRemainingPhotos();
        if (remaining === -1 || remaining > 0) {
            this.currentUser.photosUsed++;
            this.saveUserData();
            this.updateUI();
            
            this.trackEvent('photo_generated', {
                subscription_type: this.currentUser.subscriptionType,
                photos_remaining: this.getRemainingPhotos()
            });
        }
    }

    // Check if user can use the service
    canUseService() {
        if (!this.isAuthenticated()) return false;
        if (!this.hasActiveSubscription()) return false;
        
        const remaining = this.getRemainingPhotos();
        return remaining === -1 || remaining > 0; // unlimited or has photos left
    }

    // Generate unique user ID
    generateUserId() {
        return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // Simulate API call delay
    async simulateAPICall() {
        return new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1000));
    }

    // Simulate payment processing
    async simulatePaymentProcessing(amount) {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                // Simulate 95% success rate
                if (Math.random() > 0.05) {
                    resolve({ success: true, amount });
                } else {
                    reject(new Error('Payment processing failed. Please try again.'));
                }
            }, 2000 + Math.random() * 1000);
        });
    }

    // Setup event listeners
    setupEventListeners() {
        // Login/Register buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.auth-trigger')) {
                e.preventDefault();
                if (!this.isAuthenticated()) {
                    this.showLoginModal();
                } else {
                    // User is authenticated, proceed with action
                    const action = e.target.dataset.action;
                    if (action === 'generate') {
                        this.handleGenerateRequest();
                    }
                }
            }
            
            if (e.target.matches('.logout-btn')) {
                e.preventDefault();
                this.logout();
            }
            
            if (e.target.matches('.subscribe-btn')) {
                e.preventDefault();
                const planType = e.target.dataset.plan;
                this.handleSubscription(planType);
            }
        });
    }

    // Handle generate request
    handleGenerateRequest() {
        if (!this.canUseService()) {
            if (!this.hasActiveSubscription()) {
                this.showSubscriptionRequiredModal();
            } else {
                this.showNoPhotosRemainingModal();
            }
            return false;
        }
        return true;
    }

    // Handle subscription purchase
    async handleSubscription(planType) {
        if (!this.isAuthenticated()) {
            this.showLoginModal();
            return;
        }
        
        this.showPaymentModal(planType);
    }

    // Update UI based on authentication state
    updateUI() {
        this.updateNavigationUI();
        this.updateSubscriptionStatus();
        this.updateGenerateButton();
        this.updatePricingCards();
    }

    // Update navigation UI - FIXED LAYOUT
    updateNavigationUI() {
        const authElements = document.querySelectorAll('.auth-element');
        const userElements = document.querySelectorAll('.user-element');
        
        if (this.isAuthenticated()) {
            authElements.forEach(el => el.style.display = 'none');
            userElements.forEach(el => el.style.display = 'flex');
            
            // Update user info
            const userNameElements = document.querySelectorAll('.user-name');
            userNameElements.forEach(el => {
                el.textContent = this.currentUser.name || this.currentUser.email;
            });
        } else {
            authElements.forEach(el => el.style.display = 'flex');
            userElements.forEach(el => el.style.display = 'none');
        }
    }

    // Update subscription status display - IMPROVED
    updateSubscriptionStatus() {
        const statusElement = document.getElementById('subscriptionStatus');
        if (!statusElement) return;
        
        if (!this.isAuthenticated()) {
            statusElement.innerHTML = '<span class="status-inactive">Not logged in</span>';
            statusElement.style.display = 'none';
            return;
        }
        
        statusElement.style.display = 'block';
        
        if (!this.currentUser.subscriptionType) {
            statusElement.innerHTML = '<span class="status-no-plan">Choose a plan to start</span>';
        } else if (this.isInTrialPeriod()) {
            const remainingDays = this.getRemainingTrialDays();
            statusElement.innerHTML = `
                <div class="status-trial">
                    <span class="plan-name">Free Trial</span>
                    <span class="plan-details">${remainingDays} days • ${this.getRemainingPhotos()} photos left</span>
                </div>
            `;
        } else if (this.hasActiveSubscription()) {
            const plan = this.subscriptionPlans[this.currentUser.subscriptionType];
            const remaining = this.getRemainingPhotos();
            statusElement.innerHTML = `
                <div class="status-active">
                    <span class="plan-name">${plan.name}</span>
                    <span class="plan-details">${remaining === -1 ? 'Unlimited' : remaining} photos ${remaining === -1 ? '' : 'remaining'}</span>
                </div>
            `;
        } else {
            statusElement.innerHTML = '<span class="status-expired">Subscription Expired</span>';
        }
    }

    // Update generate button state
    updateGenerateButton() {
        const generateButtons = document.querySelectorAll('.generate-btn, .hero-cta');
        
        generateButtons.forEach(btn => {
            if (!this.isAuthenticated()) {
                btn.textContent = 'Sign Up to Start Creating';
                btn.classList.add('auth-trigger');
                btn.dataset.action = 'generate';
                btn.disabled = false;
            } else if (!this.canUseService()) {
                if (!this.hasActiveSubscription()) {
                    btn.textContent = 'Choose Plan to Continue';
                    btn.classList.add('subscribe-trigger');
                    btn.disabled = false;
                } else {
                    btn.textContent = 'No Photos Remaining';
                    btn.disabled = true;
                }
            } else {
                btn.innerHTML = '<i class="fas fa-magic"></i> Generate Wedding Portrait';
                btn.classList.remove('auth-trigger', 'subscribe-trigger');
                btn.disabled = false;
            }
        });
    }

    // Update pricing cards
    updatePricingCards() {
        const pricingCards = document.querySelectorAll('.pricing-card');
        
        pricingCards.forEach(card => {
            const btn = card.querySelector('.pricing-btn');
            const planType = btn.dataset.plan || 
                           (btn.href && btn.href.includes('pro') ? 'pro' : 
                            btn.href && btn.href.includes('wedding') ? 'wedding' : 'free_trial');
            
            if (!this.isAuthenticated()) {
                btn.textContent = planType === 'free_trial' ? 'Start Free Trial' : `Get ${this.subscriptionPlans[planType].name}`;
                btn.classList.add('auth-trigger');
                btn.disabled = false;
            } else if (this.currentUser.subscriptionType === planType && this.hasActiveSubscription()) {
                btn.textContent = 'Current Plan';
                btn.disabled = true;
            } else {
                btn.textContent = planType === 'free_trial' ? 'Start Free Trial' : `Upgrade to ${this.subscriptionPlans[planType].name}`;
                btn.classList.add('subscribe-btn');
                btn.dataset.plan = planType;
                btn.disabled = false;
            }
        });
    }

    // Show modals
    showLoginModal() {
        document.getElementById('loginModal').style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }

    // NEW: Subscription selection modal for new users
    showSubscriptionSelectionModal() {
        const modal = this.createModal('Welcome! Choose Your Plan', `
            <div class="subscription-selection">
                <h3>Welcome to AI Wedding Studio!</h3>
                <p>To start creating beautiful wedding portraits, please choose a subscription plan:</p>
                <div class="plan-selection-grid">
                    <div class="plan-option" data-plan="free_trial">
                        <div class="plan-badge">Most Popular</div>
                        <h4>Free Trial</h4>
                        <div class="plan-price">$0</div>
                        <div class="plan-duration">3 days</div>
                        <ul class="plan-features">
                            <li><i class="fas fa-check"></i> 3 AI-generated photos</li>
                            <li><i class="fas fa-check"></i> All dress styles</li>
                            <li><i class="fas fa-check"></i> All scene options</li>
                            <li><i class="fas fa-check"></i> Standard quality</li>
                        </ul>
                        <button class="btn-primary subscribe-btn" data-plan="free_trial">Start Free Trial</button>
                    </div>
                    <div class="plan-option" data-plan="pro">
                        <h4>Pro Package</h4>
                        <div class="plan-price">$19<span>/month</span></div>
                        <div class="plan-duration">30 days</div>
                        <ul class="plan-features">
                            <li><i class="fas fa-check"></i> 20 AI-generated photos</li>
                            <li><i class="fas fa-check"></i> All dress styles</li>
                            <li><i class="fas fa-check"></i> Ultra-high quality</li>
                            <li><i class="fas fa-check"></i> Commercial use</li>
                        </ul>
                        <button class="btn-primary subscribe-btn" data-plan="pro">Choose Pro</button>
                    </div>
                    <div class="plan-option" data-plan="wedding">
                        <h4>Wedding Package</h4>
                        <div class="plan-price">$49<span>/month</span></div>
                        <div class="plan-duration">30 days</div>
                        <ul class="plan-features">
                            <li><i class="fas fa-check"></i> Unlimited photos</li>
                            <li><i class="fas fa-check"></i> All dress styles</li>
                            <li><i class="fas fa-check"></i> Priority support</li>
                            <li><i class="fas fa-check"></i> Custom requests</li>
                        </ul>
                        <button class="btn-primary subscribe-btn" data-plan="wedding">Choose Wedding</button>
                    </div>
                </div>
                <p class="selection-note">You can upgrade or change your plan anytime.</p>
            </div>
        `);
        this.showModal(modal);
    }

    showWelcomeModal() {
        const modal = this.createModal('Welcome to AI Wedding Studio!', `
            <div class="welcome-content">
                <h3>Welcome ${this.currentUser.name}!</h3>
                <p>Your <strong>3-day free trial</strong> has started. You can create up to <strong>3 stunning wedding portraits</strong> during your trial period.</p>
                <div class="trial-info">
                    <div class="trial-stat">
                        <span class="number">${this.getRemainingTrialDays()}</span>
                        <span class="label">Days Remaining</span>
                    </div>
                    <div class="trial-stat">
                        <span class="number">${this.getRemainingPhotos()}</span>
                        <span class="label">Photos Available</span>
                    </div>
                </div>
                <p>After your trial, choose a plan that works best for you to continue creating beautiful wedding portraits!</p>
                <button class="btn-primary" onclick="authManager.closeModal()">Start Creating</button>
            </div>
        `);
        this.showModal(modal);
    }

    showSubscriptionRequiredModal() {
        const modal = this.createModal('Subscription Required', `
            <div class="subscription-required">
                <h3>Subscription Required</h3>
                <p>To continue creating beautiful wedding portraits, please choose a subscription plan.</p>
                <div class="quick-plans">
                    <div class="quick-plan">
                        <h4>Pro Package</h4>
                        <div class="price">$19/month</div>
                        <p>20 high-quality photos</p>
                        <button class="btn-primary subscribe-btn" data-plan="pro">Choose Pro</button>
                    </div>
                    <div class="quick-plan featured">
                        <h4>Wedding Package</h4>
                        <div class="price">$49/month</div>
                        <p>Unlimited photos</p>
                        <button class="btn-primary subscribe-btn" data-plan="wedding">Choose Wedding</button>
                    </div>
                </div>
                <button class="btn-secondary" onclick="authManager.closeModal()">Maybe Later</button>
            </div>
        `);
        this.showModal(modal);
    }

    showNoPhotosRemainingModal() {
        const modal = this.createModal('No Photos Remaining', `
            <div class="no-photos-remaining">
                <h3>No Photos Remaining</h3>
                <p>You've used all your photos for this subscription period. Upgrade your plan to continue creating beautiful wedding portraits!</p>
                <div class="upgrade-options">
                    <div class="upgrade-option">
                        <h4>Pro Package</h4>
                        <div class="price">$19/month</div>
                        <p>20 high-quality photos</p>
                        <button class="btn-primary subscribe-btn" data-plan="pro">Upgrade to Pro</button>
                    </div>
                    <div class="upgrade-option featured">
                        <h4>Wedding Package</h4>
                        <div class="price">$49/month</div>
                        <p>Unlimited photos</p>
                        <button class="btn-primary subscribe-btn" data-plan="wedding">Upgrade to Wedding</button>
                    </div>
                </div>
                <button class="btn-secondary" onclick="authManager.closeModal()">Maybe Later</button>
            </div>
        `);
        this.showModal(modal);
    }

    showSubscriptionExpiredModal() {
        const modal = this.createModal('Subscription Expired', `
            <div class="subscription-expired">
                <h3>Subscription Expired</h3>
                <p>Your subscription has expired. Renew now to continue creating stunning wedding portraits!</p>
                <div class="renewal-offer">
                    <p><strong>Special Renewal Offer:</strong> Get 20% off your next subscription!</p>
                    <button class="btn-primary subscribe-btn" data-plan="pro">Renew Pro - $15.20</button>
                    <button class="btn-primary subscribe-btn" data-plan="wedding">Renew Wedding - $39.20</button>
                </div>
                <button class="btn-secondary" onclick="authManager.closeModal()">Not Now</button>
            </div>
        `);
        this.showModal(modal);
    }

    showPaymentModal(planType) {
        const plan = this.subscriptionPlans[planType];
        const modal = this.createModal(`Subscribe to ${plan.name}`, `
            <div class="payment-modal">
                <div class="plan-summary">
                    <h3>${plan.name}</h3>
                    <div class="price">$${plan.price}${plan.price > 0 ? '/month' : ''}</div>
                    <ul class="features">
                        ${plan.features.map(feature => `<li><i class="fas fa-check"></i> ${feature}</li>`).join('')}
                    </ul>
                </div>
                <div class="payment-form">
                    <h4>Payment Information</h4>
                    <form id="paymentForm">
                        <div class="form-group">
                            <label>Card Number</label>
                            <input type="text" placeholder="1234 5678 9012 3456" maxlength="19">
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>Expiry</label>
                                <input type="text" placeholder="MM/YY" maxlength="5">
                            </div>
                            <div class="form-group">
                                <label>CVV</label>
                                <input type="text" placeholder="123" maxlength="3">
                            </div>
                        </div>
                        <button type="submit" class="btn-primary payment-submit" data-plan="${planType}">
                            ${plan.price > 0 ? `Subscribe for $${plan.price}/month` : 'Start Free Trial'}
                        </button>
                    </form>
                </div>
                <button class="btn-secondary" onclick="authManager.closeModal()">Cancel</button>
            </div>
        `);
        
        this.showModal(modal);
        
        // Handle payment form submission
        document.getElementById('paymentForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.processPayment(planType);
        });
    }

    async processPayment(planType) {
        const submitBtn = document.querySelector('.payment-submit');
        const originalText = submitBtn.textContent;
        
        submitBtn.textContent = 'Processing...';
        submitBtn.disabled = true;
        
        try {
            const result = await this.subscribe(planType);
            if (result.success) {
                this.closeModal();
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            alert('Payment failed: ' + error.message);
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    }

    showSubscriptionSuccessModal(plan) {
        const modal = this.createModal('Subscription Successful!', `
            <div class="subscription-success">
                <h3>Welcome to ${plan.name}!</h3>
                <p>Your subscription is now active. You can start creating amazing wedding portraits right away!</p>
                <div class="subscription-details">
                    <div class="detail">
                        <span class="label">Plan:</span>
                        <span class="value">${plan.name}</span>
                    </div>
                    <div class="detail">
                        <span class="label">Photos:</span>
                        <span class="value">${plan.photos === -1 ? 'Unlimited' : plan.photos}</span>
                    </div>
                    <div class="detail">
                        <span class="label">Duration:</span>
                        <span class="value">${plan.duration} days</span>
                    </div>
                </div>
                <button class="btn-primary" onclick="authManager.closeModal(); document.getElementById('app').scrollIntoView();">Start Creating</button>
            </div>
        `);
        this.showModal(modal);
    }

    // Modal utilities
    createModal(title, content) {
        return `
            <div class="auth-modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>${title}</h2>
                        <span class="close-modal" onclick="authManager.closeModal()">&times;</span>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                </div>
            </div>
        `;
    }

    showModal(modalContent) {
        // Remove existing modals
        const existingModals = document.querySelectorAll('.auth-modal');
        existingModals.forEach(modal => modal.remove());
        
        // Create new modal
        const modalDiv = document.createElement('div');
        modalDiv.className = 'modal-overlay';
        modalDiv.innerHTML = modalContent;
        document.body.appendChild(modalDiv);
        document.body.style.overflow = 'hidden';
    }

    closeModal() {
        const modals = document.querySelectorAll('.modal-overlay');
        modals.forEach(modal => modal.remove());
        document.body.style.overflow = 'auto';
    }

    // Analytics tracking
    trackEvent(eventName, properties = {}) {
        // Integration with analytics services
        console.log('Event tracked:', eventName, properties);
        
        // Example: Google Analytics
        if (typeof gtag !== 'undefined') {
            gtag('event', eventName, properties);
        }
        
        // Example: Custom analytics
        if (typeof analytics !== 'undefined') {
            analytics.track(eventName, properties);
        }
    }
}

// Initialize authentication manager
const authManager = new AuthManager();