// Test script to verify photo analysis functionality
const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

// Sample base64 image data (small test image)
const testPhotoUrl = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEASABIAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=";

async function testPhotoAnalysis() {
  console.log("🧪 Testing photo analysis functionality...");
  
  const testData = {
    photoUrl: testPhotoUrl,
    styles: ["chinese-traditional"],
    userId: "test-user"
  };

  try {
    console.log("📋 Test data:", {
      photoUrlLength: testData.photoUrl.length,
      photoFormat: testData.photoUrl.substring(0, 30) + "...",
      stylesCount: testData.styles.length,
      styles: testData.styles
    });

    // Create curl command with JSON data
    const jsonData = JSON.stringify(testData);
    const curlCommand = `curl -s -X POST http://localhost:3000/api/generate \\
      -H "Content-Type: application/json" \\
      -d '${jsonData.replace(/'/g, "'\\''")}' \\
      -w "\\n%{http_code}"`;

    console.log("🔄 Sending request to API...");
    
    const { stdout, stderr } = await execAsync(curlCommand);
    
    if (stderr) {
      console.error("❌ Curl error:", stderr);
      return false;
    }

    // Split response and status code
    const lines = stdout.trim().split('\n');
    const statusCode = lines[lines.length - 1];
    const responseBody = lines.slice(0, -1).join('\n');

    console.log("📥 Response status:", statusCode);

    if (statusCode === '200') {
      try {
        const data = JSON.parse(responseBody);
        
        console.log("✅ API Response successful:");
        console.log("  Request ID:", data.requestId);
        console.log("  Success:", data.success);
        console.log("  Photos generated:", data.photos?.length || 0);
        
        if (data.photos && data.photos.length > 0) {
          const photo = data.photos[0];
          console.log("  First photo:");
          console.log("    Style:", photo.style);
          console.log("    Has error:", !!photo.error);
          console.log("    Original photo included:", !!photo.originalPhoto);
        }

        return true;
        
      } catch (parseError) {
        console.error("❌ Failed to parse response:", parseError.message);
        console.log("Raw response:", responseBody.substring(0, 200) + "...");
        return false;
      }
    } else {
      console.error("❌ API returned status:", statusCode);
      console.log("Response body:", responseBody);
      return false;
    }

  } catch (error) {
    console.error("❌ Test failed:", error.message);
    return false;
  }
}

async function testInvalidPhotoFormat() {
  console.log("\n🧪 Testing invalid photo format handling...");
  
  const testData = {
    photoUrl: "invalid-photo-url",
    styles: ["western-elegant"],
    userId: "test-user"
  };

  try {
    const jsonData = JSON.stringify(testData);
    const curlCommand = `curl -s -X POST http://localhost:3000/api/generate \\
      -H "Content-Type: application/json" \\
      -d '${jsonData.replace(/'/g, "'\\''")}' \\
      -w "\\n%{http_code}"`;

    const { stdout } = await execAsync(curlCommand);
    const lines = stdout.trim().split('\n');
    const statusCode = lines[lines.length - 1];
    const responseBody = lines.slice(0, -1).join('\n');

    console.log("📥 Response status:", statusCode);

    if (statusCode === '400') {
      const data = JSON.parse(responseBody);
      console.log("✅ Correctly rejected invalid photo format");
      console.log("  Error message:", data.error);
      return true;
    } else {
      console.log("❌ Expected 400 status for invalid photo, got:", statusCode);
      return false;
    }

  } catch (error) {
    console.error("❌ Test failed:", error.message);
    return false;
  }
}

async function runTests() {
  console.log("🔍 Testing Photo Analysis Integration\n");
  
  const test1 = await testPhotoAnalysis();
  const test2 = await testInvalidPhotoFormat();
  
  console.log("\n📋 Test Summary:");
  console.log(`  Photo analysis test: ${test1 ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`  Invalid format test: ${test2 ? '✅ PASS' : '❌ FAIL'}`);
  
  if (test1 && test2) {
    console.log("\n🎉 All tests passed! Photo analysis integration is working.");
    console.log("✅ User photos are now analyzed before generation");
    console.log("✅ Invalid photo formats are properly rejected");
    console.log("✅ Generated images should better match the uploaded photo");
  } else {
    console.log("\n❌ Some tests failed. Please check the implementation.");
  }
}

runTests();
