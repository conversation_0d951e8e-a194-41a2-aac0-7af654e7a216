{"name": "ai-wedding-dress-studio", "version": "1.0.0", "description": "Transform your photos into stunning wedding portraits with AI", "main": "index.html", "scripts": {"start": "node server.js", "serve": "python3 server.py", "dev": "node server.js", "build": "echo 'No build step required - this is a static web app'", "test": "echo 'Open http://localhost:8000 in your browser to test'"}, "keywords": ["ai", "wedding", "photo", "portrait", "dress", "generator", "pwa", "web-app"], "author": "AI Wedding Dress Studio", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/ai-wedding-dress-studio.git"}, "bugs": {"url": "https://github.com/your-username/ai-wedding-dress-studio/issues"}, "homepage": "https://github.com/your-username/ai-wedding-dress-studio#readme", "engines": {"node": ">=12.0.0"}, "devDependencies": {}, "dependencies": {}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}