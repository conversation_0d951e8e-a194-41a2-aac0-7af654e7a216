// Verification script to check if all fixes are properly implemented
const fs = require('fs');
const path = require('path');

console.log("🔍 Verifying fixes...\n");

// Check 1: Verify API route has optimized prompts
console.log("1. Checking API route prompt optimization...");
const apiRoute = fs.readFileSync('src/app/api/generate/route.ts', 'utf8');
const hasForwardFacing = apiRoute.includes('facing forward towards camera');
const hasPortraitOrientation = apiRoute.includes('Portrait orientation');
console.log(`   ✅ Forward-facing prompt: ${hasForwardFacing ? 'FOUND' : 'MISSING'}`);
console.log(`   ✅ Portrait orientation: ${hasPortraitOrientation ? 'FOUND' : 'MISSING'}`);

// Check 2: Verify generating page has duplicate call prevention
console.log("\n2. Checking generating page duplicate call prevention...");
const generatingPage = fs.readFileSync('src/app/[locale]/generating/page.tsx', 'utf8');
const hasGeneratingCheck = generatingPage.includes('if (isGenerating)');
const hasSessionCheck = generatingPage.includes('if (generatedPhotosStr)');
const hasEmptyDependency = generatingPage.includes('}, []);');
console.log(`   ✅ Generating state check: ${hasGeneratingCheck ? 'FOUND' : 'MISSING'}`);
console.log(`   ✅ Session storage check: ${hasSessionCheck ? 'FOUND' : 'MISSING'}`);
console.log(`   ✅ Empty useEffect dependency: ${hasEmptyDependency ? 'FOUND' : 'MISSING'}`);

// Check 3: Verify path fixes in all pages
console.log("\n3. Checking path fixes...");
const pages = [
  'src/app/[locale]/upload/page.tsx',
  'src/app/[locale]/styles/page.tsx', 
  'src/app/[locale]/results/page.tsx'
];

pages.forEach(pagePath => {
  const content = fs.readFileSync(pagePath, 'utf8');
  const hasRelativePath = content.includes('href="../');
  const hasLocalePath = content.includes('${currentLocale}');
  console.log(`   ${path.basename(pagePath)}: ${hasRelativePath ? '❌ Still has relative paths' : '✅ Fixed'} | ${hasLocalePath ? '✅ Has locale paths' : '❌ Missing locale paths'}`);
});

// Check 4: Verify environment configuration
console.log("\n4. Checking environment configuration...");
const hasEnvExample = fs.existsSync('.env.example');
const hasEnvLocal = fs.existsSync('.env.local');
console.log(`   ✅ .env.example: ${hasEnvExample ? 'EXISTS' : 'MISSING'}`);
console.log(`   ✅ .env.local: ${hasEnvLocal ? 'EXISTS' : 'MISSING'}`);

// Check 5: Verify package.json has OpenAI dependency
console.log("\n5. Checking dependencies...");
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const hasOpenAI = packageJson.dependencies && packageJson.dependencies.openai;
console.log(`   ✅ OpenAI dependency: ${hasOpenAI ? 'INSTALLED' : 'MISSING'}`);

// Check 6: Verify session storage clearing logic
console.log("\n6. Checking session storage clearing logic...");
const uploadPage = fs.readFileSync('src/app/[locale]/upload/page.tsx', 'utf8');
const stylesPage = fs.readFileSync('src/app/[locale]/styles/page.tsx', 'utf8');

const uploadClearsSession = uploadPage.includes('sessionStorage.removeItem("selectedStyles")') && uploadPage.includes('sessionStorage.removeItem("generatedPhotos")');
const stylesClearsGenerated = stylesPage.includes('sessionStorage.removeItem("generatedPhotos")');
const retryClearsGenerated = generatingPage.includes('sessionStorage.removeItem("generatedPhotos")');

console.log(`   ✅ Upload page clears session: ${uploadClearsSession ? 'YES' : 'NO'}`);
console.log(`   ✅ Styles page clears generated: ${stylesClearsGenerated ? 'YES' : 'NO'}`);
console.log(`   ✅ Retry clears generated: ${retryClearsGenerated ? 'YES' : 'NO'}`);

console.log("\n🎉 Verification complete!");
console.log("\n📋 Summary of fixes:");
console.log("   1. ✅ Optimized prompts for front-facing bride photos");
console.log("   2. ✅ Prevented duplicate API calls with state checks");
console.log("   3. ✅ Fixed relative path issues in navigation");
console.log("   4. ✅ Added environment configuration files");
console.log("   5. ✅ Installed OpenAI SDK dependency");
console.log("   6. ✅ Fixed session storage logic to prevent cached results");
